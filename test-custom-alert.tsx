import React, { useState } from "react"
import { View, StyleSheet } from "react-native"
import { Text, Button, CustomAlert } from "./app/components"
import { 
  parseErrorMessage, 
  createRetryButtons, 
  createSuccessAlert, 
  createWarningAlert 
} from "./app/utils/alertUtils"

// Test component to demonstrate CustomAlert functionality
export const TestCustomAlert = () => {
  const [alertConfig, setAlertConfig] = useState<{
    visible: boolean
    type: "error" | "success" | "warning" | "info"
    title: string
    message: string
    buttons: any[]
  }>({
    visible: false,
    type: "info",
    title: "",
    message: "",
    buttons: [],
  })

  // Test different alert types
  const showErrorAlert = () => {
    // Simulate the API error from your example
    const mockApiError = {
      message: {
        "serializer view error": "email: fedha user with this email already exists."
      },
      success: false
    }

    const errorInfo = parseErrorMessage(mockApiError)
    setAlertConfig({
      visible: true,
      type: errorInfo.type,
      title: errorInfo.title,
      message: errorInfo.message,
      buttons: createRetryButtons(
        () => {
          console.log("Retry action triggered")
          setAlertConfig(prev => ({ ...prev, visible: false }))
        },
        () => setAlertConfig(prev => ({ ...prev, visible: false }))
      ),
    })
  }

  const showSuccessAlert = () => {
    const successConfig = createSuccessAlert(
      "Profil mis à jour avec succès",
      () => {
        console.log("Success action triggered")
        setAlertConfig(prev => ({ ...prev, visible: false }))
      }
    )
    setAlertConfig({
      visible: true,
      ...successConfig,
    })
  }

  const showWarningAlert = () => {
    const warningConfig = createWarningAlert(
      "Cette action supprimera définitivement vos données. Êtes-vous sûr de vouloir continuer ?",
      () => {
        console.log("Warning confirmed")
        setAlertConfig(prev => ({ ...prev, visible: false }))
      },
      () => setAlertConfig(prev => ({ ...prev, visible: false }))
    )
    setAlertConfig({
      visible: true,
      ...warningConfig,
    })
  }

  const showInfoAlert = () => {
    setAlertConfig({
      visible: true,
      type: "info",
      title: "Information",
      message: "Voici une information importante pour vous.",
      buttons: [
        {
          text: "Compris",
          style: "default",
          onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
        },
      ],
    })
  }

  const showGenericError = () => {
    // Test with a generic error
    const errorInfo = parseErrorMessage(null)
    setAlertConfig({
      visible: true,
      type: errorInfo.type,
      title: errorInfo.title,
      message: errorInfo.message,
      buttons: [
        {
          text: "OK",
          style: "default",
          onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
        },
      ],
    })
  }

  const showNetworkError = () => {
    // Test with network error
    const networkError = {
      response: {
        status: 500,
        data: {
          message: "Internal server error"
        }
      }
    }
    
    const errorInfo = parseErrorMessage(networkError)
    setAlertConfig({
      visible: true,
      type: errorInfo.type,
      title: errorInfo.title,
      message: errorInfo.message,
      buttons: [
        {
          text: "OK",
          style: "default",
          onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
        },
      ],
    })
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Custom Alert Test</Text>
      
      <Text style={styles.description}>
        Test the beautiful custom alert component with different types and error parsing.
      </Text>

      <View style={styles.buttonContainer}>
        <Button
          text="Show API Error (Email Exists)"
          onPress={showErrorAlert}
          style={[styles.button, styles.errorButton]}
        />

        <Button
          text="Show Success Alert"
          onPress={showSuccessAlert}
          style={[styles.button, styles.successButton]}
        />

        <Button
          text="Show Warning Alert"
          onPress={showWarningAlert}
          style={[styles.button, styles.warningButton]}
        />

        <Button
          text="Show Info Alert"
          onPress={showInfoAlert}
          style={[styles.button, styles.infoButton]}
        />

        <Button
          text="Show Generic Error"
          onPress={showGenericError}
          style={[styles.button, styles.errorButton]}
        />

        <Button
          text="Show Network Error (500)"
          onPress={showNetworkError}
          style={[styles.button, styles.errorButton]}
        />
      </View>

      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>Features Demonstrated:</Text>
        <Text style={styles.feature}>• Beautiful, professional design</Text>
        <Text style={styles.feature}>• Specific error message parsing</Text>
        <Text style={styles.feature}>• Different alert types (error, success, warning, info)</Text>
        <Text style={styles.feature}>• Custom buttons with retry functionality</Text>
        <Text style={styles.feature}>• Proper error handling for API responses</Text>
        <Text style={styles.feature}>• Fallback to generic messages when needed</Text>
      </View>

      {/* Custom Alert Component */}
      <CustomAlert
        visible={alertConfig.visible}
        type={alertConfig.type}
        title={alertConfig.title}
        message={alertConfig.message}
        buttons={alertConfig.buttons}
        onClose={() => setAlertConfig(prev => ({ ...prev, visible: false }))}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#f5f5f5",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 16,
    color: "#333",
  },
  description: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    color: "#666",
    lineHeight: 24,
  },
  buttonContainer: {
    marginBottom: 24,
  },
  button: {
    marginBottom: 12,
  },
  errorButton: {
    backgroundColor: "#e74c3c",
  },
  successButton: {
    backgroundColor: "#27ae60",
  },
  warningButton: {
    backgroundColor: "#f39c12",
  },
  infoButton: {
    backgroundColor: "#3498db",
  },
  featuresContainer: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
    color: "#333",
  },
  feature: {
    fontSize: 14,
    marginBottom: 4,
    color: "#555",
  },
})
