{"name": "<PERSON>ha", "version": "0.5.4", "private": true, "main": "index.tsx", "scripts": {"compile": "tsc --noEmit -p . --pretty", "lint": "eslint . --fix", "lint:check": "eslint .", "patch": "patch-package", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test .maestro/FavoritePodcast.yaml", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "postinstall": "patch-package", "build:ios:sim": "eas build --profile development --platform ios --local", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:prod": "cd android && ./gradlew bundleRelease && cd ..", "start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx serve dist", "prebuild:clean": "npx expo prebuild --clean"}, "dependencies": {"@expo-google-fonts/space-grotesk": "^0.2.2", "@expo/metro-runtime": "~4.0.0", "@hookform/resolvers": "^3.10.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^6.3.2", "@react-navigation/native": "^6.0.2", "@react-navigation/native-stack": "^6.0.2", "@sentry/react-native": "~6.10.0", "@shopify/flash-list": "1.7.3", "apisauce": "3.0.1", "axios": "^1.7.9", "date-fns": "^4.1.0", "expo": "^52.0.44", "expo-background-fetch": "~13.0.6", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.1", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.1", "expo-device": "~7.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.1", "expo-haptics": "~14.0.1", "expo-image-manipulator": "~13.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.2", "expo-local-authentication": "^15.0.2", "expo-localization": "~16.0.0", "expo-modules-core": "~2.2.3", "expo-notifications": "^0.29.14", "expo-screen-capture": "~7.0.1", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.10", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.3", "i": "^0.3.7", "i18next": "^23.14.0", "intl-pluralrules": "^2.0.1", "lottie-react-native": "7.1.0", "native-notify": "^4.0.9", "posthog-react-native": "^3.12.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.0.1", "react-loading-skeleton": "^3.5.0", "react-native": "0.76.9", "react-native-blob-util": "^0.21.2", "react-native-circular-progress": "^1.4.1", "react-native-date-picker": "^5.0.10", "react-native-device-info": "^14.0.4", "react-native-drawer-layout": "^4.0.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.20.2", "react-native-image-picker": "^8.0.0", "react-native-image-resizer": "^1.4.5", "react-native-keyboard-controller": "^1.12.7", "react-native-mmkv": "^2.12.2", "react-native-pager-view": "6.5.1", "react-native-paper": "^5.13.1", "react-native-paper-tabs": "^0.11.3", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-share": "^12.0.9", "react-native-svg": "15.8.0", "react-native-vision-camera": "^4.6.4", "react-native-web": "~0.19.6", "swr": "^2.3.2", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@testing-library/react-native": "^12.5.2", "@types/jest": "^29.2.1", "@types/react": "~18.3.12", "babel-jest": "^29.2.1", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-reactotron": "^0.1.2", "jest": "^29.2.1", "jest-expo": "~52.0.1", "patch-package": "^8.0.0", "postinstall-prepare": "1.0.1", "prettier": "^3.3.3", "react-test-renderer": "18.2.0", "reactotron-core-client": "^2.9.4", "reactotron-react-js": "^3.3.11", "reactotron-react-native": "^5.0.5", "reactotron-react-native-mmkv": "^0.2.6", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "~5.3.3"}}