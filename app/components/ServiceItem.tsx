/* eslint-disable react-native/no-inline-styles */
import {
  ImageStyle,
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  ImageSourcePropType,
  ImageBackground,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, type ThemedStyle } from "@/theme"
import { Button, Text } from "."

export interface ServiceItemProps {
  imageSource: ImageSourcePropType
  title?: string
  description: string
  onBuyPress: () => void
  buttonText?: string
  style?: StyleProp<ViewStyle>
  imageStyle?: StyleProp<ImageStyle>
  descriptionStyle?: StyleProp<TextStyle>
  buttonStyle?: StyleProp<ViewStyle>
  buttonTextStyle?: StyleProp<TextStyle>
}

export const ServiceItem = (props: ServiceItemProps) => {
  const {
    imageSource,
    description,
    onBuyPress,
    buttonText = "Acheter maintenant", // Default button text
    style,
    imageStyle,
    descriptionStyle,
    buttonStyle,
    title,
  } = props

  const { themed } = useAppTheme()

  // Combine base styles with optional override styles
  const $containerStyles = [themed($container), style]
  const $imageStyles = [$image, imageStyle]
  const $descriptionStyles = [themed($descriptionText), descriptionStyle]
  const $buttonStyles = [themed($button), buttonStyle]

  return (
    <View style={$containerStyles}>
      <View
        style={{
          height: 130,
          width: "100%",
          paddingBottom: spacing.xl,
        }}
      >
        <ImageBackground
          source={imageSource}
          style={$imageStyles}
          // imageStyle={styles.modalHeaderImage}
        />
      </View>
      <View
        style={{
          flex: 1,
          paddingVertical: spacing.xl,
          paddingHorizontal: spacing.md,
        }}
      >
        <Text
          style={{
            fontSize: 18,
            fontWeight: "bold",
            marginBottom: 10,
            color: colors.palette.neutral900,
          }}
        >
          {title}
        </Text>
        <Text style={$descriptionStyles}>{description}</Text>
      </View>
      <Button
        testID="login-button"
        preset="reversed"
        text={buttonText}
        style={$buttonStyles}
        onPress={onBuyPress}
      ></Button>
    </View>
  )
}
const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  alignItems: "center", // Center items horizontally
  overflow: "hidden",
  borderRadius: 16,

  // marginHorizontal: spacing.md,
  borderWidth: 0.3,
  borderColor: colors.palette.neutral500,
  backgroundColor: colors.palette.neutral100,
  elevation: 3,
  marginBottom: spacing.xl,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.5,
  shadowRadius: 8,
})

const $image: ImageStyle = {
  width: "100%",
  height: 160,
  marginBottom: 15,
  paddingBottom: 10,
  borderTopLeftRadius: 16,
  borderTopRightRadius: 16,
  borderRadius: 4,
}

const $descriptionText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.textDim, // Example text color
  // textAlign: "center",
  marginBottom: 16,
})

const $button: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral900,
  borderRadius: 16,
  elevation: 3,
  paddingVertical: spacing.md,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  width: "90%",
  bottom: spacing.md,
})
