/* eslint-disable react-native/no-inline-styles */
import { TextStyle, View, ViewStyle } from "react-native"
import RNPickerSelect from "react-native-picker-select"
import { Text } from "./Text"
import { useRef } from "react"
import { spacing, typography } from "@/theme"

export interface CustomSelectorProps {
  value?: any
  onValueChange?: any
  isitems: any
  placeholder?: any
  inputname?: string
  inputstyle?: any
}

/**
 * Describe your component here
 */

export const CustomSelector = (props: CustomSelectorProps) => {
  const { inputstyle, value, onValueChange, isitems, placeholder, inputname } = props
  const $styles = [$container, inputstyle]
  const input = useRef<RNPickerSelect>(null)

  // Add key property to each item to fix the "Each child in a list should have a unique key prop" warning
  const itemsWithKeys = Array.isArray(isitems)
    ? isitems.map((item: any) => ({
        ...item,
        key: `${item.value}-${item.label}`,
        inputLabel: item.label, // Add inputLabel for search functionality
        searchable: true // Enable search on this item
      }))
    : []

  return (
    <View style={$styles}>
      <Text style={$title}>{inputname}</Text>
      <View style={{ paddingVertical: 4 }}>
        <RNPickerSelect
          ref={input}
          value={value}
          onValueChange={onValueChange}
          items={itemsWithKeys}
          placeholder={placeholder}
          style={{
            inputAndroid: $Input,
            inputIOS: $Input,
          }}
          {...props}
        />
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  flex: 1,
  borderRadius: 15,
  marginTop: spacing.md,
  paddingHorizontal: 15,
  paddingVertical: 3,
  borderWidth: 1.5,
}

const $title: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: "#333",
}

const $Input: TextStyle = {
  fontSize: 18,
  flex: 1,
  color: "#333",
  fontWeight: "bold",
  width: "100%",
  // backgroundColor: "#fff",
  height: 40,
}
