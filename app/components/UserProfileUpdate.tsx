/* eslint-disable react-native/no-color-literals */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/jsx-no-undef */
/* eslint-disable react-native/no-inline-styles */
import { StyleProp, View, ViewStyle, Image, Alert, TouchableOpacity, TextStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { $Gstyles, colors, spacing, VerifUserBasicInfoStyle, type ThemedStyle } from "@/theme"
import { Button, FencyTextInput, Icon, Text } from "."
import { zodResolver } from "@hookform/resolvers/zod"
import { ImageLibraryOptions, launchImageLibrary } from "react-native-image-picker"
import RNFS from "react-native-fs"
import { Avatar } from "react-native-paper"
import { Controller, useForm } from "react-hook-form"
import { userBasicInfoUpdate, UserBasicSchema } from "@/services/api"
import { useStores } from "@/store/rootStore"
import { useEffect, useState } from "react"

export interface UserProfileUpdateProps {
  onCompleted: () => void
  style?: StyleProp<ViewStyle>
}

export const UserProfileUpdate = (props: UserProfileUpdateProps) => {
  const {
    auth: { user, fetchUserData },
  } = useStores()
  const { style, onCompleted } = props
  const $styles = [$container, style]
  const { themed } = useAppTheme()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedImage, setSelectedImage] = useState<string | null>(null) // Track selected image
  const { profileImage } = VerifUserBasicInfoStyle

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(UserBasicSchema),
    defaultValues: {
      first_name: "",
      middle_name: "",
      last_name: "",
      email: "",
    },
  })

  // Populate existing user data if available
  useEffect(() => {
    if (user) {
      setValue("first_name", user?.first_name || "")
      setValue("middle_name", user?.middle_name || "")
      setValue("last_name", user?.last_name || "")
      setValue("email", user?.email || "")
      // Only set the image if user has a profile picture
      if (user.profile_picture) {
        setSelectedImage(user.profile_picture)
      }
    }
  }, [setValue, user])

  // Handle Image Picker
  const handleImagePicker = () => {
    const options: ImageLibraryOptions = {
      mediaType: "photo",
      includeBase64: false,
    }

    launchImageLibrary(options, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker")
      } else if (response.errorCode) {
        console.log("ImagePicker Error: ", response.errorMessage)
      } else if (response.assets && response.assets[0].uri) {
        const imageUri = response.assets[0].uri
        setSelectedImage(imageUri)
      }
    })
  }
  // USER PROFILE GIVING ISSUES SOME HOW IT NEED TO CHNAGE IN THE BACKEND NOT SURE IT BEING CALLED BUT IT RETURNING AN ISSU

  // Handle Form Submission
  const onSubmit = async (formData: any) => {
    setIsLoading(true)

    try {
      const data = new FormData()
      data.append("first_name", formData.first_name)
      data.append("middle_name", formData.middle_name)
      data.append("last_name", formData.last_name)
      data.append("email", formData.email)

      // Append Image if selected
      if (selectedImage) {
        const filePath = selectedImage.replace("file://", "") // Ensure correct file path

        try {
          // Check if file exists
          await RNFS.stat(filePath)

          // Read file as base64
          const fileBase64 = await RNFS.readFile(filePath, "base64")

          // Convert base64 to a Blob using fetch
          const blob = await fetch(`data:image/jpeg;base64,${fileBase64}`).then((res) => res.blob())

          // Extract filename
          const fileName = filePath.split("/").pop() || "profile_picture.jpg"

          // Append as blob
          data.append("profile_picture", blob, fileName)
        } catch (fileError) {
          console.warn("File error:", fileError)
        }
      }

      // Debugging Log
      // console.log("Sending Data to API:", [...data.entries()])

      // API Call
      const result = await userBasicInfoUpdate(data)
      console.log('er', result)

      if (result?.success) {
        Alert.alert("Succès", "Profil mis à jour avec succès")
        await fetchUserData()
        onCompleted() // Move to Advance Component
      } else {
        Alert.alert(
          "Erreur",
          "La mise à jour n'a pas réussi. Veuillez réessayer.",
          [
            { text: "Annuler", style: "cancel" },
            { text: "Réessayer", onPress: () => onSubmit(formData) }
          ]
        )
      }
    } catch (e) {
      console.error("Error during form submission:", e)
      Alert.alert("Erreur", "Une erreur s'est produite lors de la mise à jour du profile")
    } finally {
      setIsLoading(false)
    }
  }
  // console.log("control", isLoading)
  return (
    <View style={$styles}>
      {/* Enhanced Profile Picture Section */}
      <View style={$profileSection}>
        <TouchableOpacity style={$avatarContainer} onPress={handleImagePicker}>
          {selectedImage ? (
            <Avatar.Image size={130} source={{ uri: selectedImage }} style={$avatar} />
          ) : (
            <View style={$placeholderAvatar}>
              <Icon icon="photocamera" size={40} color={colors.palette.neutral900} />
            </View>
          )}
          <View style={$editIconOverlay}>
            <Icon icon="edit" size={20} color={colors.palette.neutral100} />
          </View>
        </TouchableOpacity>
      </View>

      {/* Form Section */}
      <View style={$formContainer}>
        <Controller
          control={control}
          name="first_name"
          render={({ field }) => (
            <View style={$inputWrapper}>
              <FencyTextInput
                value={field.value}
                onChange={field.onChange}
                style={themed($textField)}
                inputname="Nom"
                keyboardType="default"
                placeholder="Votre nom"
                autoCapitalize="words"
                status={errors.first_name?.message ? "error" : undefined}
              />
            </View>
          )}
        />

        <Controller
          control={control}
          name="middle_name"
          render={({ field }) => (
            <View style={$inputWrapper}>
              <FencyTextInput
                value={field.value}
                onChange={field.onChange}
                style={themed($textField)}
                inputname="Postnom"
                keyboardType="default"
                placeholder="Votre postnom"
                autoCapitalize="words"
                status={errors.middle_name?.message ? "error" : undefined}
              />
            </View>
          )}
        />

        <Controller
          control={control}
          name="last_name"
          render={({ field }) => (
            <View style={$inputWrapper}>
              <FencyTextInput
                value={field.value}
                onChange={field.onChange}
                style={themed($textField)}
                inputname="Prénom"
                keyboardType="default"
                placeholder="Votre prénom"
                autoCapitalize="words"
                status={errors.last_name?.message ? "error" : undefined}
              />
            </View>
          )}
        />

        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <View style={$inputWrapper}>
              <FencyTextInput
                value={field.value}
                onChange={field.onChange}
                style={themed($textField)}
                inputname="Email"
                keyboardType="email-address"
                placeholder="<EMAIL>"
                autoCapitalize="none"
                status={errors.email?.message ? "error" : undefined}
              />
            </View>
          )}
        />
      </View>

      {/* Submit Button */}
      <View style={$buttonContainer}>
        <Button
          testID="save-profile-button"
          preset="reversed"
          text={isLoading ? "Enregistrement..." : "Sauvergarder"}
          style={[$submitButton, isLoading && $disabledButton]}
          disabled={isLoading}
          onPress={handleSubmit(onSubmit)}
        />
      </View>
    </View>
  )
}

// Enhanced styles
const $styles: ViewStyle = {
  flex: 1,
  paddingHorizontal: spacing.lg,
  backgroundColor: colors.palette.neutral100,
}

const $profileSection: ViewStyle = {
  alignItems: "center",
  // marginVertical: spacing.xl,
}

const $avatarContainer: ViewStyle = {
  position: "relative",
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  borderRadius: 65,
  // overflow: "hidden",
  // marginBottom: spacing.xl,
  // marginBottom: spacing.xs,
}

const $avatar: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
}

const $placeholderAvatar: ViewStyle = {
  width: 130,
  height: 130,
  borderRadius: 65,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  borderStyle: "dashed",
}

const $editIconOverlay: ViewStyle = {
  position: "absolute",
  right: 0,
  bottom: 0,
  backgroundColor: colors.palette.primary500,
  borderRadius: 20,
  padding: spacing.xs,
  borderWidth: 2,
  borderColor: colors.palette.neutral100,
}

const $formContainer: ViewStyle = {
  flex: 1,
}

const $inputWrapper: ViewStyle = {
  marginBottom: spacing.md,
}

const $textField: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
  borderRadius: 8,
})

const $buttonContainer: ViewStyle = {
  paddingVertical: spacing.lg,
}

const $submitButton: ViewStyle = {
  borderRadius: 10,
  height: 55,
}

const $disabledButton: ViewStyle = {
  opacity: 0.5,
}

const $container: ViewStyle = {
  flex: 1,
  // backgroundColor: colors.palette.neutral100,
}
