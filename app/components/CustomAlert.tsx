/* eslint-disable no-restricted-imports */
import React from "react"
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Dimensions,
  StatusBar,
} from "react-native"
import { Icon } from "."
import { colors, spacing, typography } from "@/theme"

const { width: screenWidth } = Dimensions.get("window")

export type AlertType = "error" | "success" | "warning" | "info"

export interface AlertButton {
  text: string
  onPress?: () => void
  style?: "default" | "cancel" | "destructive"
}

export interface CustomAlertProps {
  visible: boolean
  type?: AlertType
  title?: string
  message: string
  buttons?: AlertButton[]
  onClose?: () => void
  autoClose?: boolean
  autoCloseDelay?: number
}

export const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  type = "info",
  title,
  message,
  buttons = [{ text: "OK", style: "default" }],
  onClose,
  autoClose = false,
  autoCloseDelay = 3000,
}) => {
  // const insets = useSafeAreaInsets()

  React.useEffect(() => {
    if (visible && autoClose) {
      const timer = setTimeout(() => {
        onClose?.()
      }, autoCloseDelay)
      return () => clearTimeout(timer)
    }
  }, [visible, autoClose, autoCloseDelay, onClose])

  const getAlertConfig = () => {
    switch (type) {
      case "error":
        return {
          icon: "x" as const,
          iconColor: colors.palette.angry500,
          titleColor: colors.palette.angry500,
        }
      case "success":
        return {
          icon: "check" as const,
          iconColor: colors.palette.primary500,
          titleColor: colors.palette.neutral900,
        }
      case "warning":
        return {
          icon: "warning" as const,
          iconColor: colors.palette.secondary500,
          titleColor: colors.palette.secondary500,
        }
      case "info":
      default:
        return {
          icon: "info" as const,
          iconColor: colors.palette.accent500,
          titleColor: colors.palette.neutral900,
        }
    }
  }

  const config = getAlertConfig()

  const handleButtonPress = (button: AlertButton) => {
    button.onPress?.()
    if (button.style !== "cancel") {
      onClose?.()
    } else {
      onClose?.()
    }
  }

  const getButtonStyle = (buttonStyle?: string) => {
    switch (buttonStyle) {
      case "destructive":
        return [$button, $destructiveButton]
      case "cancel":
        return [$button, $cancelButton]
      default:
        return [$button, $defaultButton]
    }
  }

  const getButtonTextStyle = (buttonStyle?: string) => {
    switch (buttonStyle) {
      case "destructive":
        return [$buttonText, $destructiveButtonText]
      case "cancel":
        return [$buttonText, $cancelButtonText]
      default:
        return [$buttonText, $defaultButtonText]
    }
  }

  if (!visible) return null

  return (
    <>
      <StatusBar backgroundColor="rgba(0, 0, 0, 0.5)" barStyle="light-content" />
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={onClose}
        statusBarTranslucent
      >
        <View style={$overlay}>
          <View style={$container}>
            {/* Icon and Title Section */}
            <View style={$header}>
              <View style={[$iconContainer, { backgroundColor: config.iconColor + "20" }]}>
                <Icon icon={config.icon} size={32} color={config.iconColor} />
              </View>
              {title && <Text style={[$title, { color: config.titleColor }]}>{title}</Text>}
            </View>

            {/* Message Section */}
            <View style={$messageContainer}>
              <Text style={$message}>{message}</Text>
            </View>

            {/* Buttons Section */}
            <View style={$buttonsContainer}>
              {buttons.map((button, index) => (
                <TouchableOpacity
                  key={index}
                  style={getButtonStyle(button.style)}
                  onPress={() => handleButtonPress(button)}
                  activeOpacity={0.7}
                >
                  <Text style={getButtonTextStyle(button.style)}>{button.text}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    </>
  )
}

// Styles
const $overlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: spacing.lg,
}

const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.lg,
  maxWidth: screenWidth - spacing.lg * 2,
  minWidth: screenWidth * 0.8,
  shadowColor: colors.palette.neutral900,
  shadowOffset: {
    width: 0,
    height: 8,
  },
  shadowOpacity: 0.25,
  shadowRadius: 16,
  elevation: 8,
}

const $header: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.md,
}

const $iconContainer: ViewStyle = {
  width: 64,
  height: 64,
  borderRadius: 32,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.sm,
}

const $title: TextStyle = {
  fontFamily: typography.primary.semiBold,
  fontSize: 20,
  textAlign: "center",
  lineHeight: 24,
}

const $messageContainer: ViewStyle = {
  marginBottom: spacing.lg,
}

const $message: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: colors.palette.neutral700,
  textAlign: "center",
  lineHeight: 22,
}

const $buttonsContainer: ViewStyle = {
  flexDirection: "row",
  gap: spacing.sm,
}

const $button: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.lg,
  borderRadius: 8,
  alignItems: "center",
  justifyContent: "center",
  minHeight: 48,
}

const $defaultButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
}

const $cancelButton: ViewStyle = {
  backgroundColor: colors.palette.neutral300,
}

const $destructiveButton: ViewStyle = {
  backgroundColor: colors.palette.angry500,
}

const $buttonText: TextStyle = {
  fontFamily: typography.primary.semiBold,
  fontSize: 16,
}

const $defaultButtonText: TextStyle = {
  color: colors.palette.neutral100,
}

const $cancelButtonText: TextStyle = {
  color: colors.palette.neutral700,
}

const $destructiveButtonText: TextStyle = {
  color: colors.palette.neutral100,
}
