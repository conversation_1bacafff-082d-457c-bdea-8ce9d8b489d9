/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable prettier/prettier */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-unused-styles */
import React, { FC, useState, useEffect } from "react";
import { View, Text, Modal, StyleSheet, TouchableOpacity } from "react-native";
import { ViewStyle } from "react-native";
import { AppStackScreenProps } from "@/navigators";
import { AddToCartKeypad, Bheader, Button, Icon, Screen } from "@/components";
import { spacing, colors } from "@/theme";
import { useStores } from "@/store/rootStore";
import { useRoute } from "@react-navigation/native";

interface DashboardBusinessScreenProps extends AppStackScreenProps<"DashboardBusiness"> {}

export const DashboardBusinessScreen: FC<DashboardBusinessScreenProps> = ({ navigation }) => {
  const {
    mycart: { addItem },
    invoice: {fetchInvoice},
    notif: { getNotifications },
    auth: { has_business },
    elimu: { fetchCourses, courses },
    business: {
      businesses,
      fetchBusinesses,
      hasVerifiedBusiness,
      getActiveBusiness,
      getMainBusiness,
      fetchProducts
    }
  } = useStores()
  const route = useRoute();
  const [isBusinessModalVisible, setIsBusinessModalVisible] = useState(true)
  const [activeTab, setActiveTab] = useState<"sell" | "shelf">("sell")

  useEffect(() => {
    fetchBusinesses()
    getNotifications()
    fetchCourses()
    fetchInvoice(businesses[0]?.business_no)
  }, [fetchBusinesses, fetchInvoice, fetchCourses])

  // Check if business is verified and get business data
  hasVerifiedBusiness()
  getActiveBusiness() // Get active businesses
  const mainBusiness = getMainBusiness()

  // Fetch products when component mounts
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  useEffect(() => {
    // Only show modal if not on BusinessOnboarding screen and has no business
    if (!has_business && route.name !== "BusinessOnboarding") {
      setIsBusinessModalVisible(true)
    } else {
      setIsBusinessModalVisible(false)
      fetchProducts()
    }
  }, [fetchProducts, has_business, route.name])

  const handleAddItem = (item: any) => {
    if (!has_business) {
      setIsBusinessModalVisible(true)
      return
    }
    addItem(item)
  }

  // Handle tab change
  const handleTabChange = (tab: "sell" | "shelf") => {
    if (tab === "shelf") {
      navigation.navigate("MyShelf")
    } else {
      setActiveTab(tab)
    }
  }
// console.log('r',businesses[0].products)
  return (
    <>
      <Screen style={$root} preset="scroll" safeAreaEdges={["top", "bottom"]} statusBarStyle="dark">
        <Bheader
          navigation={navigation}
          businessName={mainBusiness?.name}
          businessDescription={mainBusiness?.description}
          businessLogo={mainBusiness?.image_logo ? { uri: mainBusiness.image_logo } : null}
        />

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === "sell" && styles.activeTabButton]}
            onPress={() => handleTabChange("sell")}
          >
            <View style={styles.tabContent}>
              <Icon
                icon="dialpad"
                color={activeTab === "sell" ? colors.palette.neutral900 : colors.palette.accent400}
                size={24}
              />
              <Text style={[styles.tabText, activeTab === "sell" && styles.activeTabText]}>Vendre</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tabButton, activeTab === "shelf" && styles.activeTabButton]}
            onPress={() => handleTabChange("shelf")}
          >
            <View style={styles.tabContent}>
              <Icon
                icon="shelves"
                color={activeTab === "shelf" ? colors.palette.neutral900 : colors.palette.accent400}
                size={24}
              />
              <Text style={[styles.tabText, activeTab === "shelf" && styles.activeTabText]}>Mes Produits</Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        {activeTab === "sell" && (
          <AddToCartKeypad onAddItem={handleAddItem} disabled={!has_business} />
        )}
      </Screen>

      <Modal visible={isBusinessModalVisible} transparent animationType="slide"  onRequestClose={() => {
          // Prevent closing modal if no business
          if (has_business) {
            setIsBusinessModalVisible(false)
          }
        }}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Créez votre entreprise</Text>
            <Text style={styles.modalDescription}>
            Pour commencer à accepter des paiements avec Fedha Pay, vous devez enregistrer au moins une entreprise. Complétez votre profil maintenant et débloquez tout le potentiel de Fedha pour votre activité
            </Text>
             <Button
                      testID="next-screen-button"
                      preset="reversed"
                      text="Créer mon entreprise"
                      style={$buttonStyle}
                      onPress={() => {
                        navigation.navigate("BusinessOnboarding")
                        setIsBusinessModalVisible(false)
                      }}

                    />
          </View>
        </View>
      </Modal>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  // paddingBottom: spacing.xl,
}

// Specific style for the footer Button component
const $buttonStyle: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  borderRadius: 16,
  // paddingVertical: 16, // Button preset might handle padding
  width: "100%",
}


const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: spacing.xl,
    width: "80%",
    alignItems: "center",
  },
  modalTitle: {
    // fontFamily: typography.bold,
    fontSize: 20,
    color: colors.text,
    marginBottom: spacing.md,
    textAlign: "center",
  },
  modalDescription: {
    // fontFamily: typography.regular,
    fontSize: 16,
    color: colors.textDim,
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  modalButton: {
    backgroundColor: colors.background,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: 6,
  },
  modalButtonText: {
    color: colors.palette.neutral900,
    fontSize: 16,
  },
  modalCloseButton: {
    marginTop: spacing.md,
    padding: spacing.sm,
  },
  modalCloseButtonText: {
    color: colors.textDim,
    fontSize: 14,
  },
  // Tab styles
  tabContainer: {
    flexDirection: "row",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    // marginBottom: spacing.md,
    // borderBottomWidth: 1,
    // borderBottomColor: colors.palette.neutral300,
  },
  tabButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    borderBottomWidth: 2,
    borderBottomColor: colors.palette.neutral300,
  },
  activeTabButton: {
    borderBottomColor: colors.palette.neutral900,
  },
  tabContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: spacing.xs,
  },
  tabText: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.palette.accent400,
  },
  activeTabText: {
    color: colors.palette.neutral900,
  },
});