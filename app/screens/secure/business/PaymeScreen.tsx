/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/sort-styles */
import React, { FC } from "react"
import {
  View,
  Image,
  Share,
  Alert,
  StyleSheet,
  ViewStyle,
  ImageStyle,
  TextStyle,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Button, Text } from "@/components" // Using your custom Button and Text
import { colors, spacing, typography } from "@/theme"
import { useStores } from "@/store/rootStore"

// --- Configuration ---
const logoAsset = require("../../../../assets/images/fedhaPay.png")
const qrCodeAsset = require("../../../../assets/images/fedhaQR.png")
const shareMessage = "Here's my payment QR code. Scan to pay!"
// --- End Configuration ---

interface PaymeScreenProps extends AppStackScreenProps<"Payme"> {}

export const PaymeScreen: FC<PaymeScreenProps> = () => {
  const {
    auth: { user },
    fedhapochi: { wallet },
    business: { businesses },
  } = useStores()

  const onShare = async () => {
    try {
      const result = await Share.share({
        message: shareMessage,
      })
      if (result.action === Share.sharedAction) {
        console.log("Shared successfully")
      } else if (result.action === Share.dismissedAction) {
        console.log("Share dismissed")
      }
    } catch (error: any) {
      Alert.alert("Error", "Failed to share the QR code.")
      console.error("Share error:", error.message)
    }
  }

  return (
    <Screen
      style={styles.root}
      preset="fixed"
      safeAreaEdges={["top", "bottom"]}
      statusBarStyle="dark"
    >
      <View style={styles.container}>
        {/* Logo */}

        <Image source={logoAsset} style={styles.logo} resizeMode="contain" />
        <Text style={styles.tagline}>Scan this QR code to pay me securely</Text>

        {/* QR Code Image */}
        <View style={styles.qrCodeContainer}>
          <Image
            source={{ uri: businesses[0]?.wallet.qr_code }}
            style={styles.qrCode}
            resizeMode="contain"
          />
          <View style={$walletIdContainer}>
            <Text style={$walletLabel}>FedhaPochi ID:</Text>
            <Text preset="subheading" style={$walletId}>
              {businesses[0]?.wallet.pochi}
            </Text>
            {/* {console.log(businesses[0]?.wallet.qr_code)} */}
          </View>
        </View>

        {/* Share Button */}
        <Button
          textStyle={{ color: colors.palette.neutral100 }}
          text="Share QR Code"
          onPress={onShare}
          style={styles.shareButton}
        />

        {/* Information Text */}
        <Text style={styles.infoText}>Show this code to someone to receive payment instantly.</Text>
      </View>
    </Screen>
  )
}

const $walletIdContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  marginBottom: spacing.sm,
}

const $qrImage: ImageStyle = {
  width: 200,
  height: 200,
  borderRadius: 12,
  marginVertical: spacing.md,
}

const $walletLabel: TextStyle = {
  fontFamily: typography.primary.normal,
  fontSize: 14,
  color: colors.textDim,
  marginRight: spacing.xs,
}

const $walletId: TextStyle = {
  fontFamily: typography.primary.medium,
  fontSize: 14,
  color: colors.text,
}

// --- Styles ---
const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: "#F4F4F4", // Light grey background
    alignItems: "center", // Center items horizontally on the screen
    justifyContent: "flex-start", // Align items from the top
    paddingTop: 60, // Add some top padding for better spacing
  },
  container: {
    alignItems: "center", // Center content within the container
    paddingHorizontal: 30, // More horizontal padding for spaciousness
    width: "100%", // Ensure container takes full width
  },
  logo: {
    width: 170, // Adjust logo size
    height: 60,
    marginBottom: 40,
  },
  tagline: {
    color: "#777",
    fontSize: 16,
    marginBottom: 30,
    textAlign: "center",
  },
  qrCodeContainer: {
    // backgroundColor: "#FFFFFF",
    padding: 20,
    borderRadius: 12,
    // borderWidth: 1,
    // borderColor: "#E0E0E0",
    marginBottom: 40,
    // shadowColor: "#000",
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 4,
    // elevation: 3, // For Android shadow
  },
  qrCode: {
    width: 250,
    height: 250,
    borderRadius: 12,
  },
  shareButton: {
    backgroundColor: colors.palette.neutral900, // Example blue color for the button
    borderRadius: 10,
    paddingVertical: 14,
    paddingHorizontal: 40,
    marginBottom: 30,
  },
  infoText: {
    color: "#555",
    fontSize: 14,
    textAlign: "center",
  },
})
