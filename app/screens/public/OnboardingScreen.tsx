import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface OnboardingScreenProps extends AppStackScreenProps<"Onboarding"> {}


export const OnboardingScreen: FC<OnboardingScreenProps> = () => {

  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Text text="onboarding" />
    </Screen>
  )

}

const $root: ViewStyle = {
  flex: 1,
}
