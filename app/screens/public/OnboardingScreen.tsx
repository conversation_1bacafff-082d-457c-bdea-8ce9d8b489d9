import { FC, useEffect, useRef, useState, useCallback } from "react"
import {
  ViewStyle,
  View,
  Dimensions,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  StatusBar,
  TextStyle,
  ImageStyle,
  // PanGestureHandler,
  // State,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, Icon, AutoImage } from "@/components"
import { colors, spacing, typography } from "@/theme"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import LottieView from "lottie-react-native"

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

interface OnboardingSlide {
  id: string
  type: "image" | "lottie"
  title: string
  subtitle: string
  content: any
  duration: number
  backgroundColor: string
}

const SLIDES: OnboardingSlide[] = [
  {
    id: "1",
    type: "lottie",
    title: "Bienvenue sur Fedha",
    subtitle: "Votre portefeuille numérique sécurisé pour toutes vos transactions",
    content: require("../../../assets/annimations/fedhalogoA.json"),
    duration: 4000,
    backgroundColor: colors.palette.primary500,
  },
  {
    id: "2",
    type: "image",
    title: "Envoyez de l'argent",
    subtitle: "Transférez de l'argent instantanément à vos proches en toute sécurité",
    content: require("../../../assets/images/people/womenmobile-phone.png"),
    duration: 3500,
    backgroundColor: colors.palette.secondary500,
  },
  {
    id: "3",
    type: "image",
    title: "Payez vos factures",
    subtitle: "Réglez vos factures d'électricité, d'eau et bien plus encore",
    content: require("../../../assets/images/people/business.png"),
    duration: 3500,
    backgroundColor: colors.palette.accent500,
  },
  {
    id: "4",
    type: "image",
    title: "Commencez maintenant",
    subtitle: "Rejoignez des milliers d'utilisateurs qui font confiance à Fedha",
    content: require("../../../assets/images/people/familly.png"),
    duration: 3000,
    backgroundColor: colors.palette.primary600,
  },
]

interface OnboardingScreenProps extends AppStackScreenProps<"Onboarding"> {}

export const OnboardingScreen: FC<OnboardingScreenProps> = ({ navigation }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const progressRef = useRef(new Animated.Value(0)).current
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const progressAnimationRef = useRef<Animated.CompositeAnimation | null>(null)
  const insets = useSafeAreaInsets()

  const currentSlide = SLIDES[currentIndex]
  const isLastSlide = currentIndex === SLIDES.length - 1

  // Clean up timers and animations
  const cleanupTimers = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current)
      timerRef.current = null
    }
    if (progressAnimationRef.current) {
      progressAnimationRef.current.stop()
      progressAnimationRef.current = null
    }
  }, [])

  // Start progress animation and timer
  const startProgress = useCallback(() => {
    if (isPaused) return

    cleanupTimers()
    progressRef.setValue(0)

    // Animate progress bar
    progressAnimationRef.current = Animated.timing(progressRef, {
      toValue: 1,
      duration: currentSlide.duration,
      useNativeDriver: false,
    })

    progressAnimationRef.current.start(({ finished }) => {
      if (finished && !isPaused) {
        if (isLastSlide) {
          navigation.replace("Welcome")
        } else {
          setCurrentIndex((prev) => prev + 1)
        }
      }
    })
  }, [currentSlide.duration, isPaused, isLastSlide, navigation, progressRef, cleanupTimers])

  // Pause progress
  const pauseProgress = useCallback(() => {
    setIsPaused(true)
    if (progressAnimationRef.current) {
      progressAnimationRef.current.stop()
    }
  }, [])

  // Resume progress
  const resumeProgress = useCallback(() => {
    setIsPaused(false)
  }, [])

  // Navigation handlers
  const goToNext = useCallback(() => {
    if (isLastSlide) {
      navigation.replace("Welcome")
    } else {
      setCurrentIndex((prev) => prev + 1)
    }
  }, [isLastSlide, navigation])

  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1)
    }
  }, [currentIndex])

  const handleSkip = useCallback(() => {
    navigation.replace("Welcome")
  }, [navigation])

  // Handle left tap (previous slide)
  const handleLeftTap = useCallback(() => {
    goToPrevious()
  }, [goToPrevious])

  // Handle right tap (next slide)
  const handleRightTap = useCallback(() => {
    goToNext()
  }, [goToNext])

  // Handle press in (pause)
  const handlePressIn = useCallback(() => {
    pauseProgress()
  }, [pauseProgress])

  // Handle press out (resume)
  const handlePressOut = useCallback(() => {
    resumeProgress()
  }, [resumeProgress])

  // Effects
  useEffect(() => {
    startProgress()
    return cleanupTimers
  }, [currentIndex, startProgress, cleanupTimers])

  useEffect(() => {
    if (!isPaused) {
      startProgress()
    }
  }, [isPaused, startProgress])

  // Cleanup on unmount
  useEffect(() => {
    return cleanupTimers
  }, [cleanupTimers])

  const renderMedia = () => {
    if (currentSlide.type === "lottie") {
      return (
        <LottieView
          source={currentSlide.content}
          autoPlay
          loop
          style={$lottieStyle}
          resizeMode="contain"
        />
      )
    }

    return (
      <AutoImage
        source={currentSlide.content}
        style={$imageStyle}
        maxWidth={screenWidth * 0.8}
        maxHeight={screenHeight * 0.4}
        resizeMode="contain"
      />
    )
  }

  return (
    <>
      <StatusBar
        barStyle="light-content"
        backgroundColor={currentSlide.backgroundColor}
        translucent={false}
      />
      <Screen
        style={[$root, { backgroundColor: currentSlide.backgroundColor }]}
        preset="fixed"
        safeAreaEdges={[]}
      >
        {/* Progress Bars */}
        <View style={[$progressContainer, { paddingTop: insets.top + spacing.md }]}>
          {SLIDES.map((_, index) => (
            <View key={index} style={$progressBarBackground}>
              <Animated.View
                style={[
                  $progressBarFill,
                  {
                    width:
                      index === currentIndex
                        ? progressRef.interpolate({
                            inputRange: [0, 1],
                            outputRange: ["0%", "100%"],
                          })
                        : index < currentIndex
                          ? "100%"
                          : "0%",
                  },
                ]}
              />
            </View>
          ))}
        </View>

        {/* Skip Button */}
        <TouchableOpacity style={$skipButton} onPress={handleSkip}>
          <Text style={$skipText}>Passer</Text>
        </TouchableOpacity>

        {/* Main Content Container */}
        <View style={$mainContainer}>
          {/* Touch Areas for Navigation */}
          <TouchableWithoutFeedback
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            onPress={handleLeftTap}
          >
            <View style={$leftTouchArea} />
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            onPress={handleRightTap}
          >
            <View style={$rightTouchArea} />
          </TouchableWithoutFeedback>

          {/* Content */}
          <View style={$contentContainer}>
            {/* Media Container */}
            <View style={$mediaContainer}>{renderMedia()}</View>

            {/* Text Container */}
            <View style={$textContainer}>
              <Text style={$title}>{currentSlide.title}</Text>
              <Text style={$subtitle}>{currentSlide.subtitle}</Text>
            </View>
          </View>

          {/* Pause Indicator */}
          {isPaused && (
            <View style={$pauseIndicator}>
              <View style={$pauseBar} />
              <View style={$pauseBar} />
            </View>
          )}
        </View>

        {/* Bottom Navigation (Optional) */}
        {isLastSlide && (
          <View style={$bottomContainer}>
            <TouchableOpacity style={$getStartedButton} onPress={goToNext}>
              <Text style={$getStartedText}>Commencer</Text>
              <Icon icon="caretRight" size={16} color={colors.palette.neutral900} />
            </TouchableOpacity>
          </View>
        )}
      </Screen>
    </>
  )
}

// Styles
const $root: ViewStyle = {
  // flex: 1,
}

const $progressContainer: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: spacing.md,
  gap: spacing.xs,
  zIndex: 10,
}

const $progressBarBackground: ViewStyle = {
  flex: 1,
  height: 3,
  backgroundColor: "rgba(255, 255, 255, 0.3)",
  borderRadius: 2,
  overflow: "hidden",
}

const $progressBarFill: ViewStyle = {
  height: "100%",
  backgroundColor: colors.palette.neutral100,
  borderRadius: 2,
}

const $skipButton: ViewStyle = {
  position: "absolute",
  top: spacing.xl + 40,
  right: spacing.md,
  zIndex: 10,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
}

const $skipText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
  fontFamily: typography.primary.medium,
}

const $mainContainer: ViewStyle = {
  flex: 1,
  position: "relative",
}

const $contentContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: spacing.lg,
}

const $mediaContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.xl,
}

const $lottieStyle: ViewStyle = {
  width: screenWidth * 0.6,
  height: screenWidth * 0.6,
}

const $imageStyle: ImageStyle = {
  borderRadius: 16,
}

const $textContainer: ViewStyle = {
  alignItems: "center",
  paddingBottom: spacing.xxl,
  maxWidth: screenWidth * 0.9,
}

const $title: TextStyle = {
  fontSize: 28,
  fontFamily: typography.primary.bold,
  color: colors.palette.neutral100,
  textAlign: "center",
  marginBottom: spacing.md,
  lineHeight: 34,
}

const $subtitle: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.palette.neutral200,
  textAlign: "center",
  lineHeight: 24,
  paddingHorizontal: spacing.md,
}

const $leftTouchArea: ViewStyle = {
  position: "absolute",
  left: 0,
  top: 0,
  bottom: 0,
  width: screenWidth * 0.3,
  zIndex: 5,
}

const $rightTouchArea: ViewStyle = {
  position: "absolute",
  right: 0,
  top: 0,
  bottom: 0,
  width: screenWidth * 0.7,
  zIndex: 5,
}

const $pauseIndicator: ViewStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: [{ translateX: -20 }, { translateY: -20 }],
  flexDirection: "row",
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  borderRadius: 20,
  padding: spacing.sm,
  gap: spacing.xs,
}

const $pauseBar: ViewStyle = {
  width: 4,
  height: 20,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 2,
}

const $bottomContainer: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.xl,
  alignItems: "center",
}

const $getStartedButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
  borderRadius: 25,
  gap: spacing.sm,
}

const $getStartedText: TextStyle = {
  color: colors.palette.neutral900,
  fontSize: 16,
  fontFamily: typography.primary.semiBold,
}
