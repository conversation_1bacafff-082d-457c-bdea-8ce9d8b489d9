/* eslint-disable import/no-unresolved */
import { FC, useEffect, useState, useMemo } from "react"
import {
  ViewStyle,
  View,
  Text,
  SectionList,
  TextStyle,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ImageStyle,
  ActivityIndicator,
  Dimensions,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Header, Screen, Icon } from "@/components"
import { useStores } from "@/store/rootStore"
import { colors, spacing, typography } from "@/theme"
import Share from "react-native-share"
import { getStatusConfig, getStatusIcon, getStatusOnlyIcon } from "@/utils/actions"
import React from "react"

interface HistoricScreenProps extends AppStackScreenProps<"Historic"> {}

const { height } = Dimensions.get("window")
const HEADER_HEIGHT = height * 0.1

// Add interface for transaction details modal
interface TransactionDetailsModalProps {
  visible: boolean
  transaction: any
  onClose: () => void
}

// Add interface for top-up details modal
interface TopUpDetailsModalProps {
  visible: boolean
  topup: any
  onClose: () => void
}

// Add this helper function at the top of the file, near other helper functions

// Create Transaction Details Modal Component
const TransactionDetailsModal = ({
  visible,
  transaction,
  onClose,
}: TransactionDetailsModalProps) => {
  // Early return if no transaction
  if (!transaction) return null

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // console.log(transaction)

  const formatAmount = (amount: string, currency: number) => {
    const currencySymbol = currency === 1 ? "$" : "FC"
    const formattedAmount = parseFloat(amount).toLocaleString("fr-FR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
    return `${currencySymbol} ${formattedAmount}`
  }

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case "withdraw":
        return "Retrait"
      case "deposit":
        return "Dépôt"
      case "transfer":
        return "Transfert"
      case "service":
        return "Service"
      default:
        return type
    }
  }

  // const shareReceipt = async () => {
  //   try {
  //     // Format the receipt message
  //     const message =
  //       `Reçu de Transaction FedhaPochi\n\n` +
  //       `ID: ${transaction?.id}\n` +
  //       `Montant: ${formatAmount(transaction?.amount, transaction?.currency)}\n` +
  //       `Date: ${formatDateTime(transaction?.created_at)}\n` +
  //       `Type: ${getTransactionTypeLabel(transaction?.transaction_type)}\n` +
  //       `Statut: ${transaction?.status}\n` +
  //       `De: ${transaction?.sender}\n` +
  //       `À: ${transaction?.receiver}`

  //     const shareOptions = {
  //       title: "Partager le reçu",
  //       message: message,
  //       // You can add an image URL here once you have receipt image generation
  //       // url: receiptImageUrl,
  //     }

  //     await Share.open(shareOptions)
  //   } catch (error: any) {
  //     if (error.message.includes("User did not share")) {
  //       return // User cancelled sharing
  //     }
  //     Alert.alert("Erreur", "Impossible de partager le reçu")
  //   }
  // }

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={$modalOverlay}>
        <View style={$modalContent}>
          {/* Header */}
          <View style={$modalHeader}>
            <View style={$modalHeaderContent}>
              <Icon
                icon={getStatusIcon(transaction.service).icon}
                size={32}
                // color={getStatusIcon(transaction.status, transaction.service_name).color}
                style={$headerIcon}
              />
              <View>
                <Text style={$modalTitle}>Détails de la transaction</Text>
                <Text style={$modalSubtitle}>{transaction.id}</Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={$closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          {/* Amount Section */}
          <View style={$amountSection}>
            <Text
              style={[
                $amountText,
                {
                  color:
                    transaction.transaction_action === "withdraw"
                      ? colors.palette.neutral800
                      : colors.palette.neutral800,
                },
              ]}
            >
              {formatAmount(transaction.amount, transaction.currency)}
            </Text>
            <Text style={$dateText}>{formatDateTime(transaction.created_at)}</Text>
          </View>

          {/* Details Section */}
          <ScrollView style={$detailsContainer}>
            <View style={$detailRow}>
              <Text style={$detailLabel}>Transaction: </Text>
              <Text style={$detailValue}>{transaction.service_name}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Statut</Text>
              <View style={$statusBadgeContainer}>
                <View style={$statusBadge}>
                  <Icon
                    icon={getStatusOnlyIcon(transaction.status).icon}
                    size={14}
                    color={getStatusConfig(transaction.status).color}
                    style={$statusIcon}
                  />
                  <Text style={$statusText}>{transaction.status}</Text>
                </View>
              </View>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Expéditeur</Text>
              <Text style={$detailValue}>{transaction.sender}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Destinataire</Text>
              <Text style={$detailValue}>{transaction.receiver}</Text>
            </View>

            {transaction.service_name && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Service</Text>
                <Text style={$detailValue}>{transaction.service_name}</Text>
              </View>
            )}

            {transaction.service_name && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Frais</Text>
                <Text style={$detailValue}>
                  {formatAmount(transaction.fee, transaction.currency)}
                </Text>
              </View>
            )}

            {transaction.note && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Note</Text>
                <Text style={$detailValue}>{transaction.note}</Text>
              </View>
            )}
          </ScrollView>

          {/* Footer */}
          {/* <View style={$modalFooter}>
            <TouchableOpacity style={$shareButton} onPress={shareReceipt}>
              <Icon icon="share" size={20} color={colors.palette.neutral100} />
              <Text style={$footerButtonText}>Partager le reçu</Text>
            </TouchableOpacity>
          </View> */}
        </View>
      </View>
    </Modal>
  )
}

// Create Top-Up Details Modal Component
const TopUpDetailsModal = ({ visible, topup, onClose }: TopUpDetailsModalProps) => {
  if (!topup) return null

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatAmount = (amount: string) => {
    const currencySymbol = topup.currency === 1 ? "$" : "FC"
    const formattedAmount = parseFloat(amount).toLocaleString("fr-FR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
    return `${currencySymbol} ${formattedAmount}`
  }
  // {console.log('topup', topup)}
  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={$modalOverlay}>
        <View style={$modalContent}>
          {/* Header */}
          <View style={$modalHeader}>
            <View style={$modalHeaderContent}>
              <Icon icon={getStatusIcon(topup.status).icon} size={32} style={$headerIcon} />
              <View>
                <Text style={$modalTitle}>Détails de la recharge</Text>
                <Text style={$modalSubtitle}>{topup.id}</Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={$closeButton}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>
          </View>

          {/* Amount Section */}
          <View style={$amountSection}>
            <Text style={[$amountText, { color: colors.palette.neutral800 }]}>
              {formatAmount(topup.amount)}
            </Text>
            {/* <Text style={$dateText}>{topup.amount_in_default_currency}</Text> */}
            <Text style={$dateText}>{formatDateTime(topup.created_at)}</Text>
          </View>

          {/* Details Section */}
          <ScrollView style={$detailsContainer}>
            <View style={$detailRow}>
              <Text style={$detailLabel}>Méthode de recharge</Text>
              <Text style={$detailValue}>{topup.transaction_vias}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Numéro de référence</Text>
              <Text style={$detailValue}>{topup.topup_no}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Pochi ID</Text>
              <Text style={$detailValue}>{topup.user_wallet}</Text>
            </View>

            <View style={$detailRow}>
              <Text style={$detailLabel}>Statut</Text>
              <View style={$statusBadgeContainer}>
                <View style={$statusBadge}>
                  <Icon
                    icon={getStatusOnlyIcon(topup.status).icon}
                    size={14}
                    color={getStatusConfig(topup.status).color}
                    style={$statusIcon}
                  />
                  <Text style={$statusText}>{topup.status}</Text>
                </View>
              </View>
            </View>

            {topup.note && (
              <View style={$detailRow}>
                <Text style={$detailLabel}>Note</Text>
                <Text style={$detailValue}>{topup.note}</Text>
              </View>
            )}
          </ScrollView>

          {/* Footer */}
          {/* <View style={$modalFooter}>
            <TouchableOpacity
              style={$shareButton}
            >
              <Icon icon="share" size={20} color={colors.palette.neutral100} />
              <Text style={$footerButtonText}>Partager le reçu</Text>
            </TouchableOpacity>
          </View> */}
        </View>
      </View>
    </Modal>
  )
}

export const HistoricScreen: FC<HistoricScreenProps> = ({ navigation }) => {
  const {
    transactions: { transactions, fetchTransactions, topups, topupsLoading, fetchTopups },
  } = useStores()

  const [selectedTransaction, setSelectedTransaction] = useState(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState<"transactions" | "topups">("transactions")
  const [selectedTopUp, setSelectedTopUp] = useState(null)
  const [isTopUpModalVisible, setIsTopUpModalVisible] = useState(false)

  useEffect(() => {
    fetchTransactions()
    fetchTopups()
  }, [fetchTransactions, fetchTopups])

  // console.log('tr', transactions)

  // Add initial state handling and null checking
  const groupedTransactions = useMemo(() => {
    if (!transactions || !Array.isArray(transactions)) {
      return []
    }

    return transactions.reduce((acc: any[], transaction: any) => {
      const date = new Date(transaction.created_at).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })

      const existingGroup = acc.find((group) => group.title === date)

      if (existingGroup) {
        existingGroup.data.push(transaction)
      } else {
        acc.push({
          title: date,
          data: [transaction],
        })
      }

      return acc
    }, [])
  }, [transactions])

  const modifiedSections = useMemo(() => {
    return groupedTransactions?.map((section) => ({
      ...section,
      data: section.data.map((item: any, index: number) => ({
        ...item,
        isLastInSection: index === section.data.length - 1,
      })),
    }))
  }, [groupedTransactions])

  const renderTransaction = ({ item }: any) => {
    const handlePress = () => {
      setSelectedTransaction(item)
      setIsModalVisible(true)
    }
    // console.log('x',item)
    // Update this line to pass both status and service_name
    const statusConfig = getStatusIcon(item.status, item.network)

    // Format amount with currency and sign
    const formatAmount = () => {
      // const sign = item.transaction_action === "withdraw" ? "-" : "+"
      const currencySymbol = item.currency === 1 ? "$" : "FC"
      const amount = parseFloat(item.amount).toLocaleString("fr-FR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
      return `${currencySymbol} ${amount}`
    }

    // Determine amount color based on transaction type
    const amountColor =
      item.transaction_action === "withdraw" ? colors.palette.neutral800 : colors.palette.neutral900

    return (
      <TouchableOpacity
        onPress={handlePress}
        style={[$transactionItem, item.isLastInSection && $transactionItemLastInSection]}
      >
        <View style={$statusIndicator}>
          <Icon icon={statusConfig.icon} size={32} color={statusConfig.color} />
        </View>
        <View style={$transactionContent}>
          <Text style={$description}>
            {item.service_name || item.note || item.transaction_type}
          </Text>
          <Text style={$recipient}>{item.note || "N/A"}</Text>
        </View>
        <View style={$amountContainer}>
          <Text style={[$amount, { color: amountColor }]}>{formatAmount()}</Text>
          <Text style={$timestamp}>
            {new Date(item.created_at).toLocaleTimeString("fr-FR", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </Text>
        </View>
      </TouchableOpacity>
    )
  }

  const renderSectionHeader = ({ section: { title } }: any) => (
    <View style={$sectionHeader}>
      <Text style={$sectionHeaderText}>{title}</Text>
    </View>
  )

  // Render top-up item
  const renderTopUpItem = ({ item }: { item: any }) => {
    const handlePress = () => {
      setSelectedTopUp(item)
      setIsTopUpModalVisible(true)
    }

    const statusConfig = getStatusIcon(item.status)

    const formatAmount = () => {
      const currencySymbol = item.currency === 1 ? "$" : "FC"
      const amount = parseFloat(item.amount).toLocaleString("fr-FR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
      return `${currencySymbol} ${amount}`
    }

    return (
      <TouchableOpacity
        onPress={handlePress}
        style={[$transactionItem, item.isLastInSection && $transactionItemLastInSection]}
      >
        <View style={$statusIndicator}>
          <Icon icon={statusConfig.icon} size={32} />
        </View>
        <View style={$transactionContent}>
          <Text style={$description}>Recharge via {item.transaction_vias}</Text>
          <Text style={$recipient}>{item.topup_no}</Text>
        </View>
        <View style={$amountContainer}>
          <Text style={[$amount, { color: colors.palette.neutral800 }]}>{formatAmount()}</Text>
          <Text style={$timestamp}>
            {new Date(item.created_at).toLocaleTimeString("fr-FR", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </Text>
        </View>
      </TouchableOpacity>
    )
  }

  // Group top-ups by date
  const groupedTopUps = useMemo(() => {
    if (!topups || !Array.isArray(topups)) {
      return []
    }

    return topups.reduce((acc: any[], topup: any) => {
      const date = new Date(topup.created_at).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })

      const existingGroup = acc.find((group) => group.title === date)

      if (existingGroup) {
        existingGroup.data.push(topup)
      } else {
        acc.push({
          title: date,
          data: [topup],
        })
      }

      return acc
    }, [])
  }, [topups])

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        title="Mes Transactions"
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={$root} preset="fixed" statusBarStyle="dark">
        {/* Tab Buttons */}
        <View style={$tabContainer}>
          <TouchableOpacity
            style={[$tabButton, activeTab === "transactions" && $activeTabButton]}
            onPress={() => setActiveTab("transactions")}
          >
            <View style={$tabContent}>
              <Icon
                icon="transfer"
                color={
                  activeTab === "transactions"
                    ? colors.palette.neutral900
                    : colors.palette.accent300
                }
                size={30}
              />
              <Text style={[$tabButtonText, activeTab === "transactions" && $activeTabButtonText]}>
                Transactions
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[$tabButton, activeTab === "topups" && $activeTabButton]}
            onPress={() => setActiveTab("topups")}
          >
            {/* <Text style={[$tabButtonText, activeTab === "topups" && $activeTabButtonText]}>
              
            </Text> */}
            <View style={$tabContent}>
              <Icon
                icon="addwallet"
                color={
                  activeTab === "topups" ? colors.palette.neutral900 : colors.palette.accent300
                }
                size={30}
              />
              <Text style={[$tabButtonText, activeTab === "topups" && $activeTabButtonText]}>
                Rechargements
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {activeTab === "transactions" ? (
          // Existing Transactions List
          !transactions ? (
            <View style={$loadingContainer}>
              <ActivityIndicator size="large" color={colors.palette.primary500} />
            </View>
          ) : !Array.isArray(transactions) || transactions.length === 0 ? (
            <View style={$emptyStateContainer}>
              {/* Your existing empty state for transactions */}
            </View>
          ) : (
            <SectionList
              sections={modifiedSections}
              renderItem={renderTransaction}
              renderSectionHeader={renderSectionHeader}
              stickySectionHeadersEnabled
              contentContainerStyle={$listContent}
            />
          )
        ) : // Top-ups List
        topupsLoading ? (
          <View style={$loadingContainer}>
            <ActivityIndicator size="large" color={colors.palette.primary500} />
          </View>
        ) : !Array.isArray(topups) || topups.length === 0 ? (
          <View style={$emptyStateContainer}>
            <View style={$emptyStateContent}>
              <View style={$iconContainer}>
                <Icon icon="monytrans" size={80} color={colors.palette.neutral900} />
              </View>
              <Text style={$emptyStateHeading}>{"Aucune Recharge"}</Text>
              <Text style={$emptyStateMessage}>
                {
                  "Vous n'avez pas encore effectué de recharge. Rechargez votre compte pour commencer à utiliser nos services."
                }
              </Text>
              <TouchableOpacity
                style={$emptyStateButton}
                onPress={() => navigation.navigate("Recevoir")}
              >
                <Text style={$buttonText}>{"Recharger mon compte"}</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <SectionList
            sections={groupedTopUps}
            renderItem={renderTopUpItem}
            renderSectionHeader={renderSectionHeader}
            stickySectionHeadersEnabled
            contentContainerStyle={$listContent}
          />
        )}
      </Screen>
      <TransactionDetailsModal
        visible={isModalVisible}
        transaction={selectedTransaction}
        onClose={() => {
          setIsModalVisible(false)
          setSelectedTransaction(null)
        }}
      />
      <TopUpDetailsModal
        visible={isTopUpModalVisible}
        topup={selectedTopUp}
        onClose={() => {
          setIsTopUpModalVisible(false)
          setSelectedTopUp(null)
        }}
      />
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

const $listContent: ViewStyle = {
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.xl * 2,
}

const $sectionHeader: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.sm,
  marginVertical: spacing.xs,
  borderTopLeftRadius: 12,
  borderTopRightRadius: 12,
}

const $sectionHeaderText: TextStyle = {
  ...typography.secondary,
  fontSize: 14,
  fontWeight: "600",
  color: colors.palette.neutral700,
}

const $transactionItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.sm,
  backgroundColor: colors.palette.neutral100,
  marginVertical: 1, // Reduced margin for tighter list
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $transactionItemLastInSection: ViewStyle = {
  borderBottomLeftRadius: 12,
  borderBottomRightRadius: 12,
  marginBottom: spacing.sm,
}

const $statusIndicator: ViewStyle = {
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
}

const $transactionContent: ViewStyle = {
  flex: 1,
}

const $amountContainer: ViewStyle = {
  alignItems: "flex-end",
}

const $description: TextStyle = {
  ...typography.secondary,
  fontSize: 16,
  color: colors.palette.neutral800,
  fontWeight: "600",
}

const $recipient: TextStyle = {
  ...typography.secondary,
  fontSize: 14,
  color: colors.palette.accent500,
}

const $amount: TextStyle = {
  ...typography.secondary,
  fontSize: 12,
  fontWeight: "bold",
  marginBottom: spacing.xxs,
}

const $timestamp: TextStyle = {
  ...typography.secondary,
  fontSize: 12,
  color: colors.palette.neutral900,
}

const $emptyStateContainer: ViewStyle = {
  // flex: 1,
  top: 30,
  backgroundColor: colors.palette.neutral100,
}

const $emptyStateContent: ViewStyle = {
  // flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: spacing.xl,
  marginBottom: spacing.xl * 2, // Add some bottom margin to center vertically better
}

const $iconContainer: ViewStyle = {
  width: 120,
  height: 120,
  borderRadius: 60,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.xl,
}

const $emptyStateHeading: TextStyle = {
  ...typography.secondary,
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  textAlign: "center",
  marginBottom: spacing.md,
}

const $emptyStateMessage: TextStyle = {
  ...typography.secondary,
  fontSize: 16,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginBottom: spacing.xl,
  lineHeight: 22,
}

const $emptyStateButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.xl,
  borderRadius: 8,
  marginTop: spacing.lg,
}

const $buttonText: TextStyle = {
  ...typography.secondary,
  fontSize: 16,
  color: colors.palette.neutral100,
  fontWeight: "600",
  textAlign: "center",
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  padding: spacing.lg,
  maxHeight: "80%",
}

const $modalHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $modalHeaderContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md,
}

const $headerIcon: ImageStyle = {
  marginRight: spacing.sm,
}

const $modalTitle: TextStyle = {
  ...typography.secondary,
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral800,
}

const $modalSubtitle: TextStyle = {
  ...typography.secondary,
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $closeButton: ViewStyle = {
  padding: spacing.xs,
}

const $amountSection: ViewStyle = {
  backgroundColor: colors.palette.neutral300,
  padding: spacing.md,
  borderRadius: 12,
  marginBottom: spacing.md,
}

const $amountText: TextStyle = {
  ...typography.secondary,
  fontSize: 24,
  fontWeight: "bold",
  marginBottom: spacing.xs,
}

const $dateText: TextStyle = {
  ...typography.secondary,
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $detailsContainer: ViewStyle = {
  gap: spacing.sm,
  maxHeight: "60%",
  marginBottom: spacing.md,
}

const $detailRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: spacing.xs,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $detailLabel: TextStyle = {
  ...typography.secondary,
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $detailValue: TextStyle = {
  ...typography.secondary,
  fontSize: 14,
  color: colors.palette.neutral800,
  fontWeight: "500",
  flex: 1,
  textAlign: "right",
}

const $modalFooter: ViewStyle = {
  padding: spacing.md,
  backgroundColor: colors.palette.neutral200,
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral300,
}

const $shareButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  padding: spacing.md,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral900,
}

const $footerButtonText: TextStyle = {
  ...typography.secondary,
  fontSize: 16,
  color: colors.palette.neutral100,
  fontWeight: "600",
  marginLeft: spacing.xs,
}

const $statusBadgeContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $statusBadge: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  // paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 16,
}

const $statusIcon: ImageStyle = {
  marginRight: spacing.xs,
}

const $statusText: TextStyle = {
  // ...typography.formHelper,
  fontWeight: "600",
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $tabContainer: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  height: HEADER_HEIGHT,
  // backgroundColor: colors.palette.neutral200,
}

const $tabButton: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.sm,
  borderBottomWidth: 2,
  borderBottomColor: colors.palette.neutral300,
}

const $tabContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.xs,
}

const $activeTabButton: ViewStyle = {
  borderBottomColor: colors.palette.neutral900,
}

const $tabButtonText: TextStyle = {
  ...typography.secondary,
  fontSize: 14,
  color: colors.palette.accent400,
}

const $activeTabButtonText: TextStyle = {
  color: colors.palette.neutral900,
  fontWeight: "600",
}
