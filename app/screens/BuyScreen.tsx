import { FC, useState } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { BuyAirtime, BuyElectricity, Header, Screen, ServiceItem } from "@/components"
import { colors, spacing } from "@/theme"
import { ServicesToBuyList } from "@/utils/Menus"
import React from "react"
// import { useNavigation } from "@react-navigation/native"

interface BuyScreenProps extends AppStackScreenProps<"Buy"> {}

export const BuyScreen: FC<BuyScreenProps> = ({ navigation }) => {
  const [selectedService, setSelectedService] = useState<string | null>(null)

  const handleBuyPress = (itemName: string) => {
    setSelectedService(itemName)
    console.log("Selected service:", itemName)
  }

  const handleBackToServices = () => {
    setSelectedService(null)
  }

  const renderServices = () => {
    return ServicesToBuyList.map((service, index) => (
      <ServiceItem
        key={index}
        title={service.serviceName}
        imageSource={service.serviceBannert}
        description={service.serviceDescription}
        onBuyPress={() => handleBuyPress(service.serviceName)}
      />
    ))
  }

  const renderSelectedServiceComponent = () => {
    switch (selectedService) {
      case "Forfaits & Minutes":
        return <BuyAirtime />
      case "SNEL Cash Power":
        // Replace with your SNEL component
        return <BuyElectricity navigation={navigation} />
      case "REGIDESO":
        // Replace with your REGIDESO component
        return <BuyAirtime />
      default:
        return null
    }
  }

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={selectedService ? handleBackToServices : navigation.goBack}
        title={selectedService ? "Détails d'achat" : "Achetez avec Fedha"}
        backgroundColor={colors.palette.neutral100}
      />
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        {selectedService ? renderSelectedServiceComponent() : renderServices()}
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.xl,
  paddingHorizontal: 15,
}
