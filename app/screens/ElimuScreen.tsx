/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import { FC, useEffect, useState } from "react"
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ImageBackground,
  FlatList,
  Dimensions,
  Modal,
  ActivityIndicator,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text, Icon, Header, MainCourse } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store"
import { ElimuCourse } from "@/store/ElimuStore"
import React from "react"

interface ElimuScreenProps extends AppStackScreenProps<"Elimu"> {}

// Our internal course representation
interface Course {
  id: string
  title: string
  duration: string
  thumbnail: string
  type: "video" | "quiz"
  summary?: string
}

interface Module {
  id: string
  title: string
  subtitle: string
  thumbnail: string
  courses: Course[]
}

// Helper function to generate a random duration string
const getRandomDuration = () => {
  const minutes = Math.floor(Math.random() * 20) + 5
  const seconds = Math.floor(Math.random() * 60)
  return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`
}

// Course Item Component
interface CourseItemProps {
  course: Course
  onPress: (course: Course) => void
}

const CourseItem: FC<CourseItemProps> = ({ course, onPress }) => {
  return (
    <TouchableOpacity style={styles.courseItem} activeOpacity={0.8} onPress={() => onPress(course)}>
      <Image source={{ uri: course.thumbnail }} style={styles.courseThumbnail} />
      <View style={styles.courseContent}>
        <View style={styles.courseInfo}>
          <Text style={styles.courseTitle}>{course.title}</Text>
          <View style={styles.courseMeta}>
            <Icon icon="caretRight" size={14} color={colors.palette.neutral500} />
            <Text style={styles.courseDuration}>{course.duration}</Text>
            {course.type === "quiz" && (
              <View style={styles.quizBadge}>
                <Text style={styles.quizText}>Quiz</Text>
              </View>
            )}
          </View>
        </View>
        <TouchableOpacity style={styles.playButton}>
          <Icon
            icon={course.type === "quiz" ? "caretRight" : "play"}
            size={16}
            color={colors.palette.neutral100}
          />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )
}

// Module Card Component
interface ModuleCardProps {
  module: Module
  onPress: (course: Course) => void
  onModulePress: (module: Module) => void
}

const ModuleCard: FC<ModuleCardProps> = ({ module, onPress, onModulePress }) => {
  return (
    <View style={styles.moduleCard}>
      <TouchableOpacity
        style={styles.moduleHeader}
        activeOpacity={0.9}
        onPress={() => onModulePress(module)}
      >
        <ImageBackground
          source={{ uri: module.thumbnail }}
          style={styles.moduleThumbnail}
          imageStyle={styles.moduleImage}
        >
          <View style={styles.moduleOverlay}>
            <View style={styles.moduleInfo}>
              <Text style={styles.moduleTitle}>{module.title}</Text>
              <Text style={styles.moduleSubtitle}>{module.subtitle}</Text>
            </View>
            <View style={styles.moduleProgress}>
              <View style={styles.progressBar}>
                <View style={styles.progressFill} />
              </View>
              <Text style={styles.progressText}>0/4 Complété</Text>
            </View>
          </View>
        </ImageBackground>
      </TouchableOpacity>

      <View style={styles.moduleContent}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Cours dans ce module</Text>
          <TouchableOpacity onPress={() => onModulePress(module)}>
            <Text style={styles.sectionAction}>Voir tout</Text>
          </TouchableOpacity>
        </View>

        <FlatList
          data={module.courses}
          renderItem={({ item }) => <CourseItem course={item} onPress={onPress} />}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.coursesList}
          snapToAlignment="start"
          decelerationRate="fast"
          snapToInterval={Dimensions.get("window").width * 0.85 + spacing.sm}
        />
      </View>
    </View>
  )
}

// Modal Course Item - Smaller version for the modal
interface ModalCourseItemProps {
  course: Course
  onPress: (course: Course) => void
}

const ModalCourseItem: FC<ModalCourseItemProps> = ({ course, onPress }) => {
  return (
    <TouchableOpacity
      style={styles.modalCourseItem}
      activeOpacity={0.8}
      onPress={() => onPress(course)}
    >
      <Image source={{ uri: course.thumbnail }} style={styles.modalCourseThumbnail} />
      <View style={styles.modalCourseContent}>
        <Text style={styles.modalCourseTitle} numberOfLines={2}>
          {course.title}
        </Text>
        <View style={styles.modalCourseMeta}>
          <Icon icon="caretRight" size={12} color={colors.palette.neutral500} />
          <Text style={styles.modalCourseDuration}>{course.duration}</Text>
          {course.type === "quiz" && (
            <View style={styles.quizBadge}>
              <Text style={styles.quizText}>Quiz</Text>
            </View>
          )}
        </View>
      </View>
      <TouchableOpacity style={styles.modalPlayButton}>
        <Icon
          icon={course.type === "quiz" ? "caretRight" : "play"}
          size={14}
          color={colors.palette.neutral100}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  )
}

// Module Detail Modal Component
interface ModuleDetailModalProps {
  visible: boolean
  module: Module | null
  onClose: () => void
  onCoursePress: (course: Course) => void
}

const ModuleDetailModal: FC<ModuleDetailModalProps> = ({
  visible,
  module,
  onClose,
  onCoursePress,
}) => {
  if (!module) return null

  return (
    <Modal visible={visible} animationType="slide" transparent onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Modal Header with Module Info */}
          <View style={styles.modalHeader}>
            <ImageBackground
              source={{ uri: module.thumbnail }}
              style={styles.modalHeaderBg}
              imageStyle={styles.modalHeaderImage}
            >
              <View style={styles.modalHeaderOverlay}>
                <View style={styles.modalHeaderContent}>
                  <View style={styles.modalTitleContainer}>
                    <Text style={styles.modalTitle}>{module.title}</Text>
                    <Text style={styles.modalSubtitle}>{module.subtitle}</Text>
                  </View>

                  <TouchableOpacity style={styles.modalCloseButton} onPress={onClose}>
                    <Icon icon="x" size={20} color={colors.palette.neutral100} />
                  </TouchableOpacity>
                </View>
              </View>
            </ImageBackground>
          </View>

          {/* Modal Body with Courses */}
          <View style={styles.modalBody}>
            <Text style={styles.modalSectionTitle}>Tous les cours dans ce module</Text>
            <Text style={styles.modalSectionSubtitle}>
              Faites défiler pour voir tous les cours disponibles
            </Text>

            <FlatList
              data={module.courses}
              renderItem={({ item }) => <ModalCourseItem course={item} onPress={onCoursePress} />}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.modalCoursesList}
              snapToAlignment="start"
              decelerationRate="fast"
              snapToInterval={Dimensions.get("window").width * 0.4 + spacing.xs}
            />

            <View style={styles.modalDescription}>
              <Text style={styles.modalDescriptionTitle}>À propos de ce module</Text>
              <Text style={styles.modalDescriptionText}>
                Ce module vous permettra d&#39;acquérir les compétences essentielles dans ce
                domaine. Suivez les cours dans l&#39;ordre recommandé pour une meilleure
                compréhension.
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.modalStartButton}
            onPress={() => onCoursePress(module.courses[0])}
          >
            <Text style={styles.modalStartButtonText}>Commencer le module</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  )
}

export const ElimuScreen: FC<ElimuScreenProps> = ({ navigation }) => {
  const {
    elimu: { fetchCourses, courses, loading, error },
  } = useStores()

  const [modalVisible, setModalVisible] = useState(false)
  const [selectedModule, setSelectedModule] = useState<Module | null>(null)
  const [modules, setModules] = useState<Module[]>([])

  // Transform API courses into our module format
  useEffect(() => {
    if (courses && courses.length > 0) {
      // Group courses by category
      const coursesByCategory: Record<number, ElimuCourse[]> = {}

      courses.forEach(course => {
        if (!coursesByCategory[course.categoryid]) {
          coursesByCategory[course.categoryid] = []
        }
        coursesByCategory[course.categoryid].push(course)
      })

      // Convert to modules
      const newModules: Module[] = Object.entries(coursesByCategory).map(([categoryId, categoryCourses], index) => {
        // Get a sample course to extract category info
        const sampleCourse = categoryCourses[0]

        return {
          id: categoryId,
          title: `Module ${index + 1}: ${sampleCourse.displayname}`,
          subtitle: sampleCourse.shortname,
          thumbnail: "https://picsum.photos/seed/finance101/800/600",
          courses: categoryCourses.map(course => ({
            id: course.id.toString(),
            title: course.fullname,
            duration: getRandomDuration(), // Random duration
            thumbnail: "https://loremflickr.com/800/600/?education?lock=1",
            type: Math.random() > 0.8 ? "quiz" : "video", // Randomly assign type with 20% chance of quiz
            summary: course.summary
          }))
        }
      })

      setModules(newModules)
    }
  }, [courses])

  useEffect(() => {
    fetchCourses()
  }, [fetchCourses])

  const handleCoursePress = (course: Course) => {
    console.log("Course pressed:", course.title)
    // Navigate to course details or play video
    // navigation.navigate("CourseDetails", { courseId: course.id })
  }

  const handleModulePress = (module: Module) => {
    console.log("Module pressed:", module.title)
    setSelectedModule(module)
    setModalVisible(true)
  }

  const handleCloseModal = () => {
    setModalVisible(false)
  }

  return (
    <>
      <Header title="Elimu" leftIcon="backicon" onLeftPress={() => navigation.goBack()} />

      <Screen style={styles.root} preset="fixed" statusBarStyle="dark">
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.palette.primary500} />
            <Text style={styles.loadingText}>Chargement des cours...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Icon icon="warning" size={40} color={colors.palette.angry500} />
            <Text style={styles.errorText}>Impossible de charger les cours</Text>
            <TouchableOpacity style={styles.retryButton} onPress={fetchCourses}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <ScrollView
            style={styles.container}
            contentContainerStyle={styles.content}
            showsVerticalScrollIndicator={false}
          >
            <MainCourse
              title="Défi du jour"
              subtitle="Ne manquez pas votre objectif !"
              duration="20 min"
              points="100 points"
              image={require("../../assets/images/VideoThum.png")}
              onPress={() => console.log("Challenge pressed!")}
            />

            {modules.length > 0 ? (
              modules.map((module) => (
                <ModuleCard
                  key={module.id}
                  module={module}
                  onPress={handleCoursePress}
                  onModulePress={handleModulePress}
                />
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>Aucun cours disponible</Text>
              </View>
            )}
          </ScrollView>
        )}

        {/* Module Detail Modal */}
        <ModuleDetailModal
          visible={modalVisible}
          module={selectedModule}
          onClose={handleCloseModal}
          onCoursePress={handleCoursePress}
        />
      </Screen>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    // flex: 1,
  },
  content: {
    paddingBottom: spacing.xl * 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.lg,
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: 16,
    color: colors.palette.neutral600,
  },
  errorContainer: {
    // flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.lg,
  },
  errorText: {
    marginTop: spacing.md,
    fontSize: 16,
    color: colors.palette.angry500,
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: colors.palette.primary500,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.palette.neutral100,
    fontWeight: "500",
  },
  emptyContainer: {
    padding: spacing.xl,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyText: {
    fontSize: 16,
    color: colors.palette.neutral600,
    textAlign: "center",
  },
  courseContent: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    padding: spacing.sm,
  },
  courseDuration: {
    color: colors.palette.neutral600,
    fontSize: 12,
    marginLeft: spacing.xs / 2,
    marginRight: spacing.sm,
  },
  courseInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  courseItem: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    marginRight: spacing.sm,
    overflow: "hidden",
    width: Dimensions.get("window").width * 0.85,
  },
  courseMeta: {
    alignItems: "center",
    flexDirection: "row",
  },
  coursesList: {
    paddingRight: spacing.md,
  },
  courseThumbnail: {
    height: 120,
    width: "100%",
  },
  courseTitle: {
    color: colors.palette.neutral800,
    fontSize: 14,
    fontWeight: "500",
    marginBottom: spacing.xs / 2,
  },
  // Modal styles
  modalBody: {
    flex: 1,
    padding: spacing.md,
  },
  modalCloseButton: {
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.3)",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  modalContainer: {
    backgroundColor: "rgba(0,0,0,0.5)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    height: "90%",
    overflow: "hidden",
  },
  modalCourseContent: {
    flex: 1,
    justifyContent: "space-between",
    padding: spacing.xs,
  },
  modalCourseDuration: {
    color: colors.palette.neutral600,
    fontSize: 10,
    marginLeft: spacing.xs / 2,
  },
  modalCourseItem: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    marginRight: spacing.sm,
    overflow: "hidden",
    position: "relative",
    width: Dimensions.get("window").width * 0.4,
  },
  modalCourseMeta: {
    alignItems: "center",
    flexDirection: "row",
    marginTop: spacing.xs,
  },
  modalCoursesList: {
    paddingVertical: spacing.md,
  },
  modalCourseThumbnail: {
    height: 100,
    width: "100%",
  },
  modalCourseTitle: {
    color: colors.palette.neutral800,
    fontSize: 12,
    fontWeight: "500",
  },
  modalDescription: {
    marginTop: spacing.lg,
    padding: spacing.xs,
  },
  modalDescriptionText: {
    color: colors.palette.neutral600,
    fontSize: 14,
    lineHeight: 20,
    marginTop: spacing.xs,
  },
  modalDescriptionTitle: {
    color: colors.palette.neutral800,
    fontSize: 16,
    fontWeight: "600",
    marginBottom: spacing.xs,
  },
  modalHeader: {
    height: 130,
    width: "100%",
  },
  modalHeaderBg: {
    height: "100%",
    justifyContent: "flex-end",
    width: "100%",
  },
  modalHeaderContent: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    padding: spacing.md,
  },
  modalHeaderImage: {
    borderTopLeftRadius: 14,
    borderTopRightRadius: 14,
  },
  modalHeaderOverlay: {
    backgroundColor: "rgba(0,0,0,0.5)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalPlayButton: {
    alignItems: "center",
    backgroundColor: colors.palette.primary500,
    borderRadius: 14,
    bottom: spacing.xs,
    height: 28,
    justifyContent: "center",
    position: "absolute",
    right: spacing.xs,
    width: 28,
  },
  modalSectionSubtitle: {
    color: colors.palette.neutral600,
    fontSize: 14,
    marginTop: spacing.xs / 2,
  },
  modalSectionTitle: {
    color: colors.palette.neutral800,
    fontSize: 18,
    fontWeight: "600",
  },
  modalStartButton: {
    alignItems: "center",
    backgroundColor: colors.palette.primary500,
    borderRadius: 12,
    margin: spacing.md,
    paddingVertical: spacing.md,
  },
  modalStartButtonText: {
    color: colors.palette.neutral100,
    fontSize: 16,
    fontWeight: "600",
  },
  modalSubtitle: {
    color: colors.palette.neutral300,
    fontSize: 14,
  },
  modalTitle: {
    color: colors.palette.neutral100,
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: spacing.xs / 2,
  },
  modalTitleContainer: {
    flex: 1,
    marginRight: spacing.md,
  },
  moduleCard: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 16,
    elevation: 2,
    marginBottom: spacing.xl,
    marginHorizontal: spacing.md,
    overflow: "hidden",
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  moduleContent: {
    padding: spacing.md,
  },
  moduleHeader: {
    width: "100%",
  },
  moduleImage: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  moduleInfo: {
    marginBottom: spacing.sm,
  },
  moduleOverlay: {
    backgroundColor: "rgba(0,0,0,0.5)",
    padding: spacing.md,
  },
  moduleProgress: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.xs,
  },
  moduleSubtitle: {
    color: colors.palette.neutral300,
    fontSize: 14,
  },
  moduleThumbnail: {
    height: 180,
    justifyContent: "flex-end",
  },
  moduleTitle: {
    color: colors.palette.neutral100,
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: spacing.xs / 2,
  },
  // pageSubtitle: {
  //   color: colors.palette.neutral600,
  //   fontSize: 16,
  //   marginBottom: spacing.lg,
  //   paddingHorizontal: spacing.md,
  // },
  // pageTitle: {
  //   color: colors.palette.neutral900,
  //   fontSize: 24,
  //   fontWeight: "bold",
  //   marginTop: spacing.md,
  //   paddingHorizontal: spacing.md,
  // },
  playButton: {
    alignItems: "center",
    backgroundColor: colors.palette.primary500,
    borderRadius: 16,
    height: 32,
    justifyContent: "center",
    width: 32,
  },
  progressBar: {
    backgroundColor: "rgba(255,255,255,0.3)",
    borderRadius: 2,
    flex: 1,
    height: 4,
    marginRight: spacing.sm,
  },
  progressFill: {
    backgroundColor: colors.palette.primary500,
    borderRadius: 2,
    height: "100%",
    width: "0%",
  },
  progressText: {
    color: colors.palette.neutral300,
    fontSize: 12,
  },
  quizBadge: {
    backgroundColor: colors.palette.primary200,
    borderRadius: 4,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
  },
  quizText: {
    color: colors.palette.primary600,
    fontSize: 10,
    fontWeight: "500",
  },
  root: {
    // backgroundColor: colors.background,
    flex: 1,
  },
  // searchButton: {
  //   padding: spacing.xs,
  // },
  sectionAction: {
    color: colors.palette.primary500,
    fontSize: 14,
  },
  sectionHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    color: colors.palette.neutral800,
    fontSize: 16,
    fontWeight: "600",
  },
})
