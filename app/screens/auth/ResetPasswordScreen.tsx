/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useState, useEffect } from "react"
import {
  View,
  StyleSheet,
  ImageBackground,
  Image,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Button, Icon, Screen, Text, TextField, CustomAlert } from "@/components"
import { Controller, useForm } from "react-hook-form"
import { colors, spacing } from "@/theme"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Restpassword } from "@/services/api"
import { parseErrorMessage } from "@/utils/alertUtils"

interface ResetPasswordScreenProps extends AppStackScreenProps<"ResetPassword"> {}

type ResetStep = "otp" | "username" | "password"

interface ResetPasswordFormData {
  otp: string
  username: string
  new_password: string
  confirm_password: string
}

// Validation schemas for each step
const otpSchema = z.object({
  otp: z
    .string()
    .min(4, "Code OTP requis")
    .max(6, "Code OTP trop long")
    .regex(/^\d+$/, "Le code OTP doit contenir uniquement des chiffres"),
})

const usernameSchema = z.object({
  username: z
    .string()
    .min(1, "Nom d'utilisateur requis")
    .refine((value) => {
      // Check if it's a valid email or phone number
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const phoneRegex = /^\+?243\d{9}$|^\d{9}$/
      return emailRegex.test(value) || phoneRegex.test(value)
    }, "Entrez un email valide ou un numéro de téléphone"),
})

const passwordSchema = z
  .object({
    new_password: z
      .string()
      .min(8, "Le mot de passe doit contenir au moins 8 caractères")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre",
      ),
    confirm_password: z.string().min(1, "Confirmation du mot de passe requise"),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Les mots de passe ne correspondent pas",
    path: ["confirm_password"],
  })

export const ResetPasswordScreen: FC<ResetPasswordScreenProps> = ({ navigation }) => {
  const [loading, setLoading] = useState(false)
  const [currentStep, setCurrentStep] = useState<ResetStep>("otp")
  const [isTyping, setIsTyping] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Custom Alert State
  const [alertConfig, setAlertConfig] = useState<{
    visible: boolean
    type: "error" | "success" | "warning" | "info" | "network"
    title: string
    message: string
    buttons: any[]
  }>({
    visible: false,
    type: "info",
    title: "",
    message: "",
    buttons: [],
  })

  // Store form data across steps
  const [formData, setFormData] = useState<ResetPasswordFormData>({
    otp: "",
    username: "",
    new_password: "",
    confirm_password: "",
  })

  // Get current schema based on step
  const getCurrentSchema = () => {
    switch (currentStep) {
      case "otp":
        return otpSchema
      case "username":
        return usernameSchema
      case "password":
        return passwordSchema
      default:
        return otpSchema
    }
  }

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    getValues,
    trigger,
    setValue,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(getCurrentSchema()),
    defaultValues: formData,
    mode: "onChange",
  })

  const otp = watch("otp")
  const username = watch("username")
  const newPassword = watch("new_password")
  const confirmPassword = watch("confirm_password")

  // Sync form values with stored data when step changes
  useEffect(() => {
    // Set form values from stored data
    setValue("otp", formData.otp)
    setValue("username", formData.username)
    setValue("new_password", formData.new_password)
    setValue("confirm_password", formData.confirm_password)
  }, [currentStep, formData, setValue])

  // Check if current step is valid
  const isCurrentStepValid = () => {
    switch (currentStep) {
      case "otp":
        return otp && otp.length >= 4 && otp.length <= 6
      case "username":
        return username && username.length > 0
      case "password":
        return (
          newPassword &&
          confirmPassword &&
          newPassword === confirmPassword &&
          newPassword.length >= 8
        )
      default:
        return false
    }
  }

  const isButtonDisabled = !isCurrentStepValid() || loading

  // Step navigation functions
  const goToNextStep = async () => {
    const isValid = await trigger()
    if (!isValid) return

    // Save current step data before moving to next step
    const currentValues = getValues()
    setFormData((prev) => ({ ...prev, ...currentValues }))

    switch (currentStep) {
      case "otp":
        setCurrentStep("username")
        break
      case "username":
        setCurrentStep("password")
        break
      case "password":
        // Submit the form
        handleSubmit(onSubmit)()
        break
    }
  }

  const goToPreviousStep = () => {
    switch (currentStep) {
      case "username":
        setCurrentStep("otp")
        break
      case "password":
        setCurrentStep("username")
        break
      case "otp":
        navigation.goBack()
        break
    }
  }

  // Form submission
  const onSubmit = async (_data: ResetPasswordFormData) => {
    try {
      setLoading(true)

      // Get the latest form values and combine with stored data
      const currentValues = getValues()
      const completeData = { ...formData, ...currentValues }

      console.log("Complete form data:", completeData)

      // Format username if it's a phone number
      let formattedUsername = completeData.username
      if (/^\d{9}$/.test(completeData.username)) {
        formattedUsername = `${completeData.username}`
      }

      const resetData = {
        username: formattedUsername,
        otp: completeData.otp,
        new_password: completeData.new_password,
        confirm_password: completeData.confirm_password,
      }

      console.log("Resetting password with data:", resetData)

      const result = await Restpassword(resetData)

      if (result.success) {
        // Navigate directly to login screen on success
        navigation.navigate("Login")
      } else {
        const errorInfo = parseErrorMessage(result)
        setAlertConfig({
          visible: true,
          type: errorInfo.type,
          title: errorInfo.title,
          message: errorInfo.message,
          buttons: [
            {
              text: "OK",
              style: "default",
              onPress: () => setAlertConfig((prev) => ({ ...prev, visible: false })),
            },
          ],
        })
      }
    } catch (error) {
      console.error("Error during password reset:", error)
      const errorInfo = parseErrorMessage(error)
      setAlertConfig({
        visible: true,
        type: errorInfo.type,
        title: errorInfo.title,
        message: errorInfo.message,
        buttons: [
          {
            text: "OK",
            style: "default",
            onPress: () => setAlertConfig((prev) => ({ ...prev, visible: false })),
          },
        ],
      })
    } finally {
      setLoading(false)
    }
  }

  // Get step info
  const getStepInfo = () => {
    switch (currentStep) {
      case "otp":
        return {
          title: "Code de vérification",
          subtitle: "Entrez le code de vérification envoyé à votre téléphone ou email",
          step: 1,
          totalSteps: 3,
        }
      case "username":
        return {
          title: "Nom d'utilisateur",
          subtitle: "Entrez votre numéro de téléphone ou adresse email",
          step: 2,
          totalSteps: 3,
        }
      case "password":
        return {
          title: "Nouveau mot de passe",
          subtitle: "Créez un nouveau mot de passe sécurisé",
          step: 3,
          totalSteps: 3,
        }
      default:
        return {
          title: "Réinitialisation",
          subtitle: "",
          step: 1,
          totalSteps: 3,
        }
    }
  }

  const stepInfo = getStepInfo()

  return (
    <ImageBackground
      source={require("../../../assets/images/bgdeem.png")}
      style={styles.background}
      resizeMode="cover"
    >
      <Screen
        backgroundColor="transparent"
        style={styles.root}
        preset="scroll"
        safeAreaEdges={["top"]}
        StatusBarProps={{ backgroundColor: colors.palette.neutral900 }}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <TouchableOpacity onPress={goToPreviousStep} style={styles.backButton}>
            <Icon icon="backicon" size={34} color={colors.palette.neutral900} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate("Helpcenter")}
            style={styles.helpButton}
          >
            <Icon icon="helpcenter" size={34} color={colors.palette.neutral900} />
          </TouchableOpacity>
        </View>

        {/* Centered Logo */}
        <View style={styles.logoContainer}>
          <Image
            style={styles.headerLogo}
            source={require("../../../assets/images/logo.png")}
            resizeMode="contain"
          />
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            {[1, 2, 3].map((step) => (
              <View
                key={step}
                style={[styles.progressStep, step <= stepInfo.step && styles.progressStepActive]}
              />
            ))}
          </View>
          <Text style={styles.progressText}>
            Étape {stepInfo.step} sur {stepInfo.totalSteps}
          </Text>
        </View>

        {/* Title Section */}
        <View style={styles.titleContainer}>
          <Text text={stepInfo.title} preset="heading" style={styles.title} />
          <Text text={stepInfo.subtitle} style={styles.subtitle} />
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          <View style={styles.formContainer}>
            {/* Step Content */}
            {currentStep === "otp" && (
              <View style={styles.inputGroup}>
                <Text text="Code de vérification" preset="formLabel" style={styles.inputLabel} />
                <Controller
                  control={control}
                  name="otp"
                  render={({ field: { value, onChange }, fieldState }) => (
                    <TextField
                      value={value}
                      onChangeText={(text) => {
                        setIsTyping(true)
                        setTimeout(() => setIsTyping(false), 1000)
                        const formatted = text.replace(/\D/g, "")
                        if (formatted.length <= 6) {
                          onChange(formatted)
                        }
                      }}
                      containerStyle={styles.input}
                      autoCapitalize="none"
                      onFocus={() => setIsTyping(true)}
                      onBlur={() => setIsTyping(false)}
                      keyboardType="numeric"
                      placeholderTextColor={colors.palette.neutral400}
                      autoCorrect={false}
                      maxLength={6}
                      placeholder="000000"
                      textAlign="center"
                      style={styles.otpInput}
                      status={
                        fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                      }
                      helper={
                        fieldState.error && fieldState.isDirty && !isTyping
                          ? fieldState.error.message
                          : undefined
                      }
                    />
                  )}
                />
              </View>
            )}

            {currentStep === "username" && (
              <View style={styles.inputGroup}>
                <Text text="Nom d'utilisateur" preset="formLabel" style={styles.inputLabel} />
                <Controller
                  control={control}
                  name="username"
                  render={({ field: { value, onChange }, fieldState }) => (
                    <TextField
                      value={value}
                      onChangeText={onChange}
                      containerStyle={styles.input}
                      autoCapitalize="none"
                      onFocus={() => setIsTyping(true)}
                      onBlur={() => setIsTyping(false)}
                      keyboardType="default"
                      placeholderTextColor={colors.palette.neutral400}
                      autoCorrect={false}
                      placeholder="Email ou numéro de téléphone"
                      status={
                        fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                      }
                      helper={
                        fieldState.error && fieldState.isDirty && !isTyping
                          ? fieldState.error.message
                          : undefined
                      }
                    />
                  )}
                />
              </View>
            )}

            {currentStep === "password" && (
              <>
                <View style={styles.inputGroup}>
                  <Text text="Nouveau mot de passe" preset="formLabel" style={styles.inputLabel} />
                  <Controller
                    control={control}
                    name="new_password"
                    render={({ field: { value, onChange }, fieldState }) => (
                      <TextField
                        value={value}
                        onChangeText={onChange}
                        containerStyle={styles.input}
                        autoCapitalize="none"
                        onFocus={() => setIsTyping(true)}
                        onBlur={() => setIsTyping(false)}
                        secureTextEntry={!showPassword}
                        placeholderTextColor={colors.palette.neutral400}
                        autoCorrect={false}
                        placeholder="Nouveau mot de passe"
                        RightAccessory={() => (
                          <TouchableOpacity
                            style={styles.eyeIcon}
                            onPress={() => setShowPassword(!showPassword)}
                          >
                            <Icon
                              icon={showPassword ? "view" : "hidden"}
                              size={24}
                              color={colors.palette.neutral600}
                            />
                          </TouchableOpacity>
                        )}
                        status={
                          fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                        }
                        helper={
                          fieldState.error && fieldState.isDirty && !isTyping
                            ? fieldState.error.message
                            : undefined
                        }
                      />
                    )}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text
                    text="Confirmer le mot de passe"
                    preset="formLabel"
                    style={styles.inputLabel}
                  />
                  <Controller
                    control={control}
                    name="confirm_password"
                    render={({ field: { value, onChange }, fieldState }) => (
                      <TextField
                        value={value}
                        onChangeText={onChange}
                        containerStyle={styles.input}
                        autoCapitalize="none"
                        onFocus={() => setIsTyping(true)}
                        onBlur={() => setIsTyping(false)}
                        secureTextEntry={!showConfirmPassword}
                        placeholderTextColor={colors.palette.neutral400}
                        autoCorrect={false}
                        placeholder="Confirmer le mot de passe"
                        RightAccessory={() => (
                          <TouchableOpacity
                            style={styles.eyeIcon}
                            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            <Icon
                              icon={showConfirmPassword ? "view" : "hidden"}
                              size={24}
                              color={colors.palette.neutral600}
                            />
                          </TouchableOpacity>
                        )}
                        status={
                          fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                        }
                        helper={
                          fieldState.error && fieldState.isDirty && !isTyping
                            ? fieldState.error.message
                            : undefined
                        }
                      />
                    )}
                  />
                </View>
              </>
            )}

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              {/* Back Button (only show if not on first step) */}
              {currentStep !== "otp" && (
                <Button
                  testID="back-button"
                  preset="default"
                  text="Précédent"
                  style={styles.backStepButton}
                  onPress={goToPreviousStep}
                />
              )}

              {/* Continue/Submit Button */}
              <Button
                testID="continue-button"
                preset="reversed"
                text={
                  loading
                    ? "Chargement..."
                    : currentStep === "password"
                      ? "Réinitialiser le mot de passe"
                      : "Continuer"
                }
                style={[
                  styles.continueButton,
                  isButtonDisabled && styles.disabledButton,
                  currentStep !== "otp" && styles.continueButtonWithBack,
                ]}
                disabled={isButtonDisabled}
                onPress={currentStep === "password" ? handleSubmit(onSubmit) : goToNextStep}
              >
                {loading && <ActivityIndicator size="small" color="white" />}
              </Button>
            </View>
          </View>
        </View>

        {/* Custom Alert */}
        <CustomAlert
          visible={alertConfig.visible}
          type={alertConfig.type}
          title={alertConfig.title}
          message={alertConfig.message}
          buttons={alertConfig.buttons}
          onClose={() => setAlertConfig((prev) => ({ ...prev, visible: false }))}
        />
      </Screen>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  backButton: {
    borderRadius: 20,
    padding: spacing.xs,
  },
  backStepButton: {
    backgroundColor: colors.palette.neutral200,
    borderColor: colors.palette.neutral300,
    borderRadius: 16,
    borderWidth: 1,
    paddingVertical: spacing.md,
    width: "100%",
  },
  background: {
    flex: 1,
    width: "100%",
  },
  buttonContainer: {
    alignItems: "center",
    gap: spacing.md,
    marginTop: spacing.md,
  },
  continueButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    elevation: 3,
    paddingVertical: spacing.lg,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    width: "100%",
  },
  continueButtonWithBack: {
    marginTop: 0,
  },
  disabledButton: {
    opacity: 0.5,
  },
  eyeIcon: {
    alignItems: "center",
    justifyContent: "center",
    paddingRight: spacing.sm,
    paddingTop: spacing.md,
  },
  formContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 24,
    elevation: 5,
    padding: spacing.lg,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
  },
  headerLogo: {
    height: 80,
    width: 120,
  },
  helpButton: {
    borderRadius: 20,
    padding: spacing.xs,
  },
  input: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    borderWidth: 0,
  },
  inputGroup: {
    marginBottom: spacing.xl,
  },
  inputLabel: {
    color: colors.palette.neutral900,
    fontSize: 14,
    fontWeight: "600",
    marginBottom: spacing.sm,
  },
  logoContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginVertical: spacing.lg,
  },
  mainContent: {
    flex: 1,
  },
  otpInput: {
    fontSize: 22,
    fontWeight: "bold",
    letterSpacing: 8,
  },
  progressBar: {
    flexDirection: "row",
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  progressContainer: {
    alignItems: "center",
    marginBottom: spacing.xl,
  },
  progressStep: {
    backgroundColor: colors.palette.neutral300,
    borderRadius: 2,
    height: 4,
    width: 60,
  },
  progressStepActive: {
    backgroundColor: colors.palette.neutral900,
  },
  progressText: {
    color: colors.palette.neutral600,
    fontSize: 14,
    fontWeight: "500",
  },
  root: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  subtitle: {
    color: colors.palette.neutral600,
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
  },
  title: {
    color: colors.palette.neutral900,
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  titleContainer: {
    alignItems: "center",
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.md,
  },
})
