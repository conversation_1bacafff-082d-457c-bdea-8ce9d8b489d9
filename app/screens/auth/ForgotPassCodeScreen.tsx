/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/sort-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-unused-styles */
import { FC, useState } from "react"
import {
  View,
  StyleSheet,
  ImageBackground,
  Image,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Button, Icon, Screen, Text, TextField } from "@/components"
import { Controller, useForm } from "react-hook-form"
import { colors, spacing } from "@/theme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { zodResolver } from "@hookform/resolvers/zod"
import { ForgetpasswordSchema } from "@/services/api"

interface ForgotPassCodeScreenProps extends AppStackScreenProps<"ForgotPassCode"> {}

interface ForgotPassCodeFormData {
  phone_number: string
}

export const ForgotPassCodeScreen: FC<ForgotPassCodeScreenProps> = ({ navigation }) => {
  const [loading, setLoading] = useState(false)
  const $bottomContainerInsets = useSafeAreaInsetsStyle(["bottom"])
  const [isPassTyping, setIsPassTyping] = useState(false)
  const [isTyping, setIsTyping] = useState(false)

  // const {
  //   control,
  //   handleSubmit,
  //   formState: { errors },
  //   watch,
  // } = useForm({
  //   defaultValues: {
  //     phone_number: "",
  //   },
  // })

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ForgotPassCodeFormData>({
    resolver: zodResolver(ForgetpasswordSchema),
    defaultValues: {
      phone_number: "",
    },
    mode: "onChange", // This will enable real-time validation
  })

  const phoneNumber = watch("phone_number")
  const isButtonDisabled = !phoneNumber || phoneNumber.length < 9 || loading

  const isValidPhoneNumber = (phone: string): boolean => {
    return phone.length === 9 && !phone.startsWith("0") && /^\d{9}$/.test(phone)
  }

  const validatePhoneNumber = (phone: string) => {
    if (!phone) return true

    if (phone.startsWith("0")) {
      return "Ne pas inclure le 0 au début"
    }

    // Only show error for complete numbers
    if (phone.length === 9) {
      if (!/^\d{9}$/.test(phone)) {
        return "Le numéro doit contenir 9 chiffres"
      }
      return true // Explicitly return true for valid 9-digit numbers
    }

    // Don't show errors while typing incomplete numbers
    return true
  }

  const handleResetPassword = async (data: { phone_number: any }) => {
    try {
      setLoading(true)
      // Add your reset password logic here
      console.log("Reset password for:", data.phone_number)
      // Navigate to OTP verification screen or show success message
      navigation.navigate("OtpVerfication")
    } catch (error) {
      // setErrorMessage("Une erreur s'est produite. Veuillez réessayer.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <ImageBackground
      source={require("../../../assets/images/bgdeem.png")}
      style={styles.background}
      resizeMode="cover"
    >
      <Screen
        backgroundColor="transparent"
        style={styles.root}
        preset="scroll"
        safeAreaEdges={["top"]}
        StatusBarProps={{ backgroundColor: colors.palette.neutral900 }}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <Icon
            icon="backicon"
            size={34}
            color={colors.palette.neutral900}
            onPress={() => navigation.goBack()}
          />
          <TouchableOpacity
            onPress={() => navigation.navigate("Helpcenter")}
            style={styles.helpButton}
          >
            <Icon icon="helpcenter" size={34} color={colors.palette.neutral900} />
          </TouchableOpacity>
        </View>
        <View>
          <Image
            style={styles.headerLogo}
            source={require("../../../assets/images/logo.png")}
            resizeMode="contain"
          />
        </View>

        <View style={styles.titleContainer}>
          <Text text="Mot de passe oublié?" preset="heading" style={styles.title} />
          <Text
            text="Entrez votre numéro de téléphone pour réinitialiser votre mot de passe"
            style={styles.subtitle}
          />
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          {/* Form Container */}
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text text="Numéro de téléphone" preset="formLabel" style={styles.inputLabel} />
              <Controller
                control={control}
                name="phone_number"
                rules={{
                  validate: validatePhoneNumber,
                }}
                render={({ field: { value, onChange }, fieldState }) => (
                  <TextField
                    value={value}
                    onChangeText={(text) => {
                      setIsTyping(true)
                      setTimeout(() => setIsTyping(false), 1000)

                      // Remove non-digits and leading zeros
                      const formatted = text.replace(/\D/g, "").replace(/^0+/, "")
                      if (formatted.length <= 9) {
                        onChange(formatted)
                      }
                    }}
                    containerStyle={[styles.input, isTyping && { borderWidth: 1 }]}
                    autoCapitalize="none"
                    onFocus={() => setIsTyping(true)}
                    onBlur={async () => {
                      setIsTyping(false)

                      // if (value.length === 9) {
                      //   const carrier = await checkProvider(value)
                      //   console.log("Carrier:", carrier)
                      //   // Optionally show the carrier name somewhere in UI
                      // }
                    }}
                    keyboardType="numeric"
                    placeholderTextColor={colors.palette.neutral400}
                    autoCorrect={false}
                    maxLength={9}
                    placeholder="00 000 0000"
                    LeftAccessory={() => (
                      <View style={styles.phonePrefix}>
                        <Image
                          source={require("../../../assets/images/flags/cd.png")}
                          style={styles.flagIcon}
                          resizeMode="cover"
                        />
                        <Text style={styles.countryCode}>+243</Text>
                      </View>
                    )}
                    status={
                      fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                    }
                    helper={
                      fieldState.error && fieldState.isDirty && !isTyping
                        ? fieldState.error.message
                        : undefined
                    }
                  />
                )}
              />
            </View>

            <View style={styles.buttonContainer}>
              <Button
                testID="reset-password-button"
                preset="reversed"
                text={loading ? "Chargement..." : "Réinitialiser le mot de passe"}
                style={[styles.resetButton, isButtonDisabled && styles.disabledButton]}
                disabled={isButtonDisabled}
                onPress={handleSubmit(handleResetPassword)}
              >
                {loading && <ActivityIndicator size="small" color="white" />}
              </Button>
            </View>
          </View>
        </View>
      </Screen>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width: "100%",
  },
  bottomContainer: {
    paddingBottom: spacing.lg,
  },
  buttonContainer: {
    alignItems: "center",
  },
  countryCode: {
    color: colors.palette.neutral900,
    fontSize: 16,
    fontWeight: "600",
  },
  disabledButton: {
    opacity: 0.5,
  },
  flagIcon: {
    borderRadius: 12,
    height: 24,
    width: 24,
  },
  titleContainer: {
    // marginTop: spacing.xl * 2,
    marginBottom: spacing.xl,
  },
  resetButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    paddingVertical: spacing.md,
    width: "100%",
  },
  forgotPassword: {
    color: colors.palette.primary600,
    fontSize: 14,
    marginBottom: spacing.xl,
    textAlign: "right",
  },
  formContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 24,
    // elevation: 5,
    padding: spacing.xs,
    // shadowColor: colors.palette.neutral900,
    // shadowOffset: { width: 0, height: 4 },
    // shadowOpacity: 0.1,
    // shadowRadius: 12,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    // paddingBottom: spacing.md,
    // paddingTop: spacing.xl,
  },
  headerLogo: {
    height: 90,
    width: 120,
  },
  helpButton: {
    // backgroundColor: colors.palette.neutral500,
    borderRadius: 20,
    padding: spacing.xs,
  },
  input: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    borderWidth: 0,
  },
  inputGroup: {
    marginBottom: spacing.xl,
  },
  inputLabel: {
    color: colors.palette.neutral900,
    fontSize: 14,
    fontWeight: "600",
    marginBottom: spacing.xs,
  },
  loginButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    elevation: 3,
    paddingVertical: spacing.md,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    width: "100%",
  },
  mainContent: {
    flex: 1,
    paddingTop: spacing.xl * 1.5,
  },
  phonePrefix: {
    alignItems: "center",
    flexDirection: "row",
    gap: spacing.xs,
    paddingLeft: spacing.sm,
    top: spacing.md,
  },
  registerContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingVertical: spacing.lg,
  },
  subtitle: {
    color: colors.palette.neutral600,
    fontSize: 16,
    lineHeight: 22,
  },
  registerLink: {
    color: colors.palette.primary500,
    fontSize: 14,
    fontWeight: "600",
  },
  registerText: {
    color: colors.palette.neutral900,
    fontSize: 14,
  },
  root: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  title: {
    color: colors.palette.neutral900,
    fontSize: 18,
    fontWeight: "bold",
    marginTop: spacing.xl,
  },
  watcherIcon: {
    right: spacing.sm,
    top: 20,
  },
})
