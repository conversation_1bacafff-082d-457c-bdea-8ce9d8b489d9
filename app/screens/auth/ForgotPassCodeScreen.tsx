/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useState } from "react"
import {
  View,
  StyleSheet,
  ImageBackground,
  Image,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Button, Icon, Screen, Text, TextField, CustomAlert } from "@/components"
import { Controller, useForm } from "react-hook-form"
import { colors, spacing } from "@/theme"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Forgetpassword } from "@/services/api"
import { parseErrorMessage, createSuccessAlert } from "@/utils/alertUtils"

interface ForgotPassCodeScreenProps extends AppStackScreenProps<"ForgotPassCode"> {}

type ContactMethod = "phone" | "email"

interface ForgotPassCodeFormData {
  phone_number: string
  email: string
}

// Create schemas for both phone and email
const phoneSchema = z.object({
  phone_number: z
    .string()
    .min(9, "Numéro de téléphone invalide")
    .max(9, "Numéro de téléphone trop long")
    .regex(/^\d+$/, "Le numéro de téléphone doit contenir uniquement des chiffres"),
})

const emailSchema = z.object({
  email: z.string().min(1, "Email requis").email("Format d'email invalide"),
})

export const ForgotPassCodeScreen: FC<ForgotPassCodeScreenProps> = ({ navigation }) => {
  const [loading, setLoading] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [activeTab, setActiveTab] = useState<ContactMethod>("phone")

  // Custom Alert State
  const [alertConfig, setAlertConfig] = useState<{
    visible: boolean
    type: "error" | "success" | "warning" | "info" | "network"
    title: string
    message: string
    buttons: any[]
  }>({
    visible: false,
    type: "info",
    title: "",
    message: "",
    buttons: [],
  })

  // Dynamic schema based on active tab
  const currentSchema = activeTab === "phone" ? phoneSchema : emailSchema

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<ForgotPassCodeFormData>({
    resolver: zodResolver(currentSchema),
    defaultValues: {
      phone_number: "",
      email: "",
    },
    mode: "onChange",
  })

  const phoneNumber = watch("phone_number")
  const email = watch("email")

  const currentValue = activeTab === "phone" ? phoneNumber : email
  const isButtonDisabled =
    !currentValue ||
    (activeTab === "phone" && phoneNumber.length < 9) ||
    (activeTab === "email" && !email.includes("@")) ||
    loading

  // Handle tab switching
  const handleTabSwitch = (tab: ContactMethod) => {
    setActiveTab(tab)
    reset() // Clear form when switching tabs
  }

  const validatePhoneNumber = (phone: string) => {
    if (!phone) return true

    if (phone.startsWith("0")) {
      return "Ne pas inclure le 0 au début"
    }

    if (phone.length === 9) {
      if (!/^\d{9}$/.test(phone)) {
        return "Le numéro doit contenir 9 chiffres"
      }
      return true
    }

    return true
  }

  const handleResetPassword = async (data: ForgotPassCodeFormData) => {
    try {
      setLoading(true)

      // Prepare the username based on active tab
      const username = activeTab === "phone" ? `+243${data.phone_number}` : data.email

      console.log("Reset password for:", username)

      // Call the API with object
      const result = await Forgetpassword({ username })

      if (result.success) {
        const successConfig = createSuccessAlert(
          "Code de réinitialisation envoyé avec succès. Vérifiez votre téléphone ou email.",
          () => {
            setAlertConfig((prev) => ({ ...prev, visible: false }))
            navigation.navigate("OtpVerfication")
          },
        )
        setAlertConfig({
          visible: true,
          ...successConfig,
        })
      } else {
        const errorInfo = parseErrorMessage(result)
        setAlertConfig({
          visible: true,
          type: errorInfo.type,
          title: errorInfo.title,
          message: errorInfo.message,
          buttons: [
            {
              text: "OK",
              style: "default",
              onPress: () => setAlertConfig((prev) => ({ ...prev, visible: false })),
            },
          ],
        })
      }
    } catch (error) {
      console.error("Error during password reset:", error)
      const errorInfo = parseErrorMessage(error)
      setAlertConfig({
        visible: true,
        type: errorInfo.type,
        title: errorInfo.title,
        message: errorInfo.message,
        buttons: [
          {
            text: "OK",
            style: "default",
            onPress: () => setAlertConfig((prev) => ({ ...prev, visible: false })),
          },
        ],
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <ImageBackground
      source={require("../../../assets/images/bgdeem.png")}
      style={styles.background}
      resizeMode="cover"
    >
      <Screen
        backgroundColor="transparent"
        style={styles.root}
        preset="scroll"
        safeAreaEdges={["top"]}
        statusBarStyle="dark"
        // StatusBarProps={{ backgroundColor: transparent }}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <Icon
            icon="backicon"
            size={34}
            color={colors.palette.neutral900}
            onPress={() => navigation.goBack()}
          />
          <TouchableOpacity
            onPress={() => navigation.navigate("Helpcenter")}
            style={styles.helpButton}
          >
            <Icon icon="helpcenter" size={34} color={colors.palette.neutral900} />
          </TouchableOpacity>
        </View>

        {/* Centered Logo */}
        <View style={styles.logoContainer}>
          <Image
            style={styles.headerLogo}
            source={require("../../../assets/images/logo.png")}
            resizeMode="contain"
          />
        </View>

        {/* Title Section */}
        <View style={styles.titleContainer}>
          <Text text="Mot de passe oublié?" preset="heading" style={styles.title} />
          <Text
            text={`Entrez votre ${activeTab === "phone" ? "numéro de téléphone" : "adresse email"} pour réinitialiser votre mot de passe`}
            style={styles.subtitle}
          />
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          {/* Form Container */}
          <View style={styles.formContainer}>
            {/* Tab Selector */}
            <View style={styles.tabContainer}>
              <TouchableOpacity
                style={[styles.tab, activeTab === "phone" && styles.activeTab]}
                onPress={() => handleTabSwitch("phone")}
              >
                <Icon
                  icon="smartphone"
                  size={20}
                  color={
                    activeTab === "phone" ? colors.palette.neutral100 : colors.palette.neutral600
                  }
                />
                <Text style={[styles.tabText, activeTab === "phone" && styles.activeTabText]}>
                  Téléphone
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.tab, activeTab === "email" && styles.activeTab]}
                onPress={() => handleTabSwitch("email")}
              >
                <Icon
                  icon="email"
                  size={20}
                  color={
                    activeTab === "email" ? colors.palette.neutral100 : colors.palette.neutral600
                  }
                />
                <Text style={[styles.tabText, activeTab === "email" && styles.activeTabText]}>
                  Email
                </Text>
              </TouchableOpacity>
            </View>

            {/* Input Section */}
            <View style={styles.inputGroup}>
              {activeTab === "phone" ? (
                <>
                  <Text text="Numéro de téléphone" preset="formLabel" style={styles.inputLabel} />
                  <Controller
                    control={control}
                    name="phone_number"
                    rules={{ validate: validatePhoneNumber }}
                    render={({ field: { value, onChange }, fieldState }) => (
                      <TextField
                        value={value}
                        onChangeText={(text) => {
                          setIsTyping(true)
                          setTimeout(() => setIsTyping(false), 1000)

                          const formatted = text.replace(/\D/g, "").replace(/^0+/, "")
                          if (formatted.length <= 9) {
                            onChange(formatted)
                          }
                        }}
                        containerStyle={styles.input}
                        autoCapitalize="none"
                        onFocus={() => setIsTyping(true)}
                        onBlur={() => setIsTyping(false)}
                        keyboardType="numeric"
                        placeholderTextColor={colors.palette.neutral400}
                        autoCorrect={false}
                        maxLength={9}
                        placeholder="00 000 0000"
                        LeftAccessory={() => (
                          <View style={styles.phonePrefix}>
                            <Image
                              source={require("../../../assets/images/flags/cd.png")}
                              style={styles.flagIcon}
                              resizeMode="cover"
                            />
                            <Text style={styles.countryCode}>+243</Text>
                          </View>
                        )}
                        status={
                          fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                        }
                        helper={
                          fieldState.error && fieldState.isDirty && !isTyping
                            ? fieldState.error.message
                            : undefined
                        }
                      />
                    )}
                  />
                </>
              ) : (
                <>
                  <Text text="Adresse email" preset="formLabel" style={styles.inputLabel} />
                  <Controller
                    control={control}
                    name="email"
                    render={({ field: { value, onChange }, fieldState }) => (
                      <TextField
                        value={value}
                        onChangeText={onChange}
                        containerStyle={styles.input}
                        autoCapitalize="none"
                        onFocus={() => setIsTyping(true)}
                        onBlur={() => setIsTyping(false)}
                        keyboardType="email-address"
                        placeholderTextColor={colors.palette.neutral400}
                        autoCorrect={false}
                        placeholder="<EMAIL>"
                        LeftAccessory={() => (
                          <View style={styles.emailPrefix}>
                            <Icon icon="email" size={20} color={colors.palette.neutral600} />
                          </View>
                        )}
                        status={
                          fieldState.error && fieldState.isDirty && !isTyping ? "error" : undefined
                        }
                        helper={
                          fieldState.error && fieldState.isDirty && !isTyping
                            ? fieldState.error.message
                            : undefined
                        }
                      />
                    )}
                  />
                </>
              )}
            </View>

            {/* Submit Button */}
            <View style={styles.buttonContainer}>
              <Button
                testID="reset-password-button"
                preset="reversed"
                text={loading ? "Chargement..." : "Réinitialiser le mot de passe"}
                style={[styles.resetButton, isButtonDisabled && styles.disabledButton]}
                disabled={isButtonDisabled}
                onPress={handleSubmit(handleResetPassword)}
              >
                {loading && <ActivityIndicator size="small" color="white" />}
              </Button>
            </View>
          </View>
        </View>

        {/* Custom Alert */}
        <CustomAlert
          visible={alertConfig.visible}
          type={alertConfig.type}
          title={alertConfig.title}
          message={alertConfig.message}
          buttons={alertConfig.buttons}
          onClose={() => setAlertConfig((prev) => ({ ...prev, visible: false }))}
        />
      </Screen>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  activeTab: {
    backgroundColor: colors.palette.neutral900,
  },
  activeTabText: {
    color: colors.palette.neutral100,
  },
  background: {
    flex: 1,
    width: "100%",
  },
  buttonContainer: {
    alignItems: "center",
    marginTop: spacing.md,
  },
  countryCode: {
    color: colors.palette.neutral900,
    fontSize: 16,
    fontWeight: "600",
  },
  disabledButton: {
    opacity: 0.5,
  },
  emailPrefix: {
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: spacing.sm,
    paddingTop: spacing.md,
  },
  flagIcon: {
    borderRadius: 12,
    height: 24,
    width: 24,
  },
  formContainer: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 24,
    elevation: 5,
    padding: spacing.lg,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
  },
  headerLogo: {
    height: 100,
    width: 140,
  },
  helpButton: {
    borderRadius: 20,
    padding: spacing.xs,
  },
  input: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    borderWidth: 0,
  },
  inputGroup: {
    marginBottom: spacing.xl,
  },
  inputLabel: {
    color: colors.palette.neutral900,
    fontSize: 14,
    fontWeight: "600",
    marginBottom: spacing.sm,
  },
  logoContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginVertical: spacing.lg,
  },
  mainContent: {
    flex: 1,
  },
  phonePrefix: {
    alignItems: "center",
    flexDirection: "row",
    gap: spacing.xs,
    paddingLeft: spacing.sm,
    paddingTop: spacing.md,
  },
  resetButton: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    elevation: 3,
    paddingVertical: spacing.lg,
    shadowColor: colors.palette.neutral900,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    width: "100%",
  },
  root: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  subtitle: {
    color: colors.palette.neutral600,
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
  },
  tab: {
    alignItems: "center",
    borderRadius: 12,
    flex: 1,
    flexDirection: "row",
    gap: spacing.xs,
    justifyContent: "center",
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.md,
  },
  tabContainer: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 16,
    flexDirection: "row",
    marginBottom: spacing.xl,
    padding: spacing.xs,
  },
  tabText: {
    color: colors.palette.neutral600,
    fontSize: 14,
    fontWeight: "600",
  },
  title: {
    color: colors.palette.neutral900,
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  titleContainer: {
    alignItems: "center",
    marginBottom: spacing.xl * 1.5,
    paddingHorizontal: spacing.md,
  },
})
