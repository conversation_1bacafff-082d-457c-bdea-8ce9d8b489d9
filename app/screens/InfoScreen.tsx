/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
import { FC, useState } from "react"
import { Dimensions, TextStyle, View, ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  ActionIcon,
  BannerHero,
  Header,
  PortalView,
  Screen,
  TarrifItem,
  Tarrifs,
  Text,
} from "@/components"
import React from "react"
import { colors } from "@/theme"
import { infoFeatures } from "@/utils/Menus"
// import { useNavigation } from "@react-navigation/native"

interface InfoScreenProps extends AppStackScreenProps<"Info"> {}

const tarrifDataFromApi: TarrifItem[] = [
  { title: "Ouverture de compte Business est Personnel", value: { cdf: 0, usd: 0 } },
  { title: "Frais de tenue de compte", value: { cdf: 0, usd: 0 } },
  { title: "Rechargement", value: { cdf: 0.3, usd: 0 } },
  {
    title: "Récapitulatif annuel des frais, délivré électroniquement dans l'Application Fedha",
    value: { cdf: 0, usd: 0.5 },
  },
  {
    title: "Relevé de compte mensuel électronique disponible dans l'Application Fedha",
    value: { cdf: 150, usd: 0.1 },
  }, // Example with non-zero values
  { title: "Service non dispo", value: {} }, // Example where keys might be missing
]

// const tarrifData = [

//   {
//     label: "Solde minimum",
//     value: "",
//     currency: "",
//   },
//   {
//     label: "Recharge par carte bancaire (instantanée)",
//     value: "",
//     currency: "",
//   },
//   {
//     label: "Clôture de compte",
//     value: "",
//     currency: "",
//   },
//   {
//     label: "Relevé de compte mensuel électronique disponible dans l'Application Fedha",
//     value: "",
//     currency: "",
//   },
//   {
//     label: "Récapitulatif annuel des frais, délivré électroniquement dans l'Application Fedha",
//     value: "",
//     currency: "",
//   },
// ]

export const InfoScreen: FC<InfoScreenProps> = ({ navigation }) => {
  const bgimge = require("../../assets/images/banners/infoBnner.png")

  const [modalState, setModalState] = useState({
    visible: false,
    content: null as React.ReactNode | null,
    iscontent: "",
  })

  // State for the coming soon modal
  const [comingSoonModal, setComingSoonModal] = useState({
    visible: false,
    serviceName: "",
    serviceIcon: "",
    description: "",
  })

  const openModal = (title: string, content: React.ReactNode) => {
    setModalState({ visible: true, iscontent: title, content })
  }

  const closeModal = () => {
    setModalState({ visible: false, iscontent: "", content: null })
  }

  const handleServicePress = (service: any) => {
    console.log("service", service)
    switch (service.serviceType) {
      case "FAQ":
        // openModal("Payez un bénéficiaire", <Beneficiaires />)
        navigation.navigate("Beneficiary")
        break
      case "ElectricityBillsPayment":
        // Handle regular transfer
        openModal("Payer vos factures d'électricité", <></>)
        break
      case "Tarifs":
        openModal(
          "Tarifs & Fraits",
          <Tarrifs
            title="Grille Tarifaire Détaillée"
            lastUpdated="Mise à jour : 27 Mai 2025, 17:50"
            data={tarrifDataFromApi}
            valueKeys={["cdf", "usd"]} // Mandatory: maps to item.value.cdf and item.value.usd
            currencyHeaders={["FC", "$"]} // Optional: column titles, defaults to ["FC", "$"]
          />,
        )
        break
      default:
        console.log("Service not implemented:", service.serviceType)
    }
  }
  return (
    <>
      <Header
        leftIcon={"backicon"}
        onLeftPress={navigation.goBack}
        title="Informations "
        backgroundColor="white"
      />
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        <BannerHero
          backgroundImage={bgimge}
          textColor={colors.palette.neutral100}
          title={`Info`}
          subtitle={`Un seul espace.${"\n"}Toutes les infos.`}
        />
        <View style={$ServiceContainer}>
          {infoFeatures.map((item, index) => {
            return (
              <ActionIcon
                key={index}
                icon={item.iconName}
                title={item.serviceName}
                onPress={() => handleServicePress(item)}
              />
            )
          })}
        </View>
      </Screen>
      <PortalView
        visible={modalState.visible}
        // status={loading}
        icon={"backicon"}
        title={modalState.iscontent}
        onClose={closeModal}
      >
        <View>{modalState.content}</View>
      </PortalView>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

const $ServiceContainer: ViewStyle = {
  paddingTop: 25,
  flex: 1,
  flexDirection: "row",
  flexWrap: "wrap",
  alignItems: "flex-start",
  justifyContent: "flex-start",
  paddingHorizontal: 8,
  gap: 8,
}

// Coming Soon Modal Styles
const { width } = Dimensions.get("window")

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContainer: ViewStyle = {
  width: width * 0.85,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  padding: 20,
  alignItems: "center",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
}

const $closeButton: ViewStyle = {
  position: "absolute",
  top: 10,
  right: 10,
  padding: 5,
  zIndex: 1,
}

const $iconContainer: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: colors.palette.primary100,
  justifyContent: "center",
  alignItems: "center",
  marginTop: 20,
  marginBottom: 15,
}

const $title: TextStyle = {
  fontSize: 22,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  textAlign: "center",
  marginBottom: 5,
}

const $comingSoonText: TextStyle = {
  fontSize: 18,
  color: colors.palette.primary500,
  fontWeight: "600",
  textAlign: "center",
  marginBottom: 15,
}

const $description: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginBottom: 20,
  paddingHorizontal: 10,
}

const $animationContainer: ViewStyle = {
  height: 150,
  width: "100%",
  marginBottom: 20,
  justifyContent: "center",
  alignItems: "center",
}

const $gifPlaceholder: ViewStyle = {
  width: 200,
  height: 150,
  backgroundColor: colors.palette.primary100,
  borderRadius: 12,
  justifyContent: "center",
  alignItems: "center",
}

const $placeholderText: TextStyle = {
  marginTop: 10,
  color: colors.palette.primary500,
  fontWeight: "600",
}

const $button: ViewStyle = {
  minWidth: 200,
  marginTop: 10,
}
