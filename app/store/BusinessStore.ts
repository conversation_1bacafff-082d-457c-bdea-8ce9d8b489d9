import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { storage } from "@/utils/storage"
import axiosInstance from "@/config/axiosInstance"
import type { Business, Product } from "@/services/api/api.types"
import { BusinessSchema, BusinessListSchema } from "@/services/api/api.types"

interface BusinessState {
  businesses: Business[]
  categories: any[]
  products: Product[]
  productsLoading: boolean
  loading: boolean
  error: string | null

  // Actions
  fetchBusinesses: () => Promise<void>
  getBusinessById: (businessId: string) => Promise<Business | null>
  fetchBusinessCategories: () => Promise<void>
  fetchProducts: () => Promise<void>
  hasVerifiedBusiness: () => boolean
  getActiveBusiness: () => Business[]
  getMainBusiness: () => Business | null
}

export const useBusinessStore = create<BusinessState>()(
  persist(
    (set, get) => ({
      businesses: [],
      categories: [],
      products: [],
      productsLoading: false,
      loading: false,
      error: null,

      async fetchBusinesses() {
        try {
          set({ loading: true, error: null })
          const response = await axiosInstance.get("/business/view/all/")
          // console.log("r", response.data)
          // Validate response data using Zod schema
          const validatedBusinesses = BusinessListSchema.parse(response.data)

          set({
            businesses: validatedBusinesses,
            loading: false,
          })
        } catch (error: any) {
          console.error("Failed to fetch businesses:", error)
          set({
            error: error.response?.data?.message || "Failed to fetch businesses",
            loading: false,
          })
        }
      },

      async getBusinessById(businessId: string) {
        try {
          set({ loading: true, error: null })
          const response = await axiosInstance.get(`/business/view/${businessId}/`)

          // Validate single business response
          const validatedBusiness = BusinessSchema.parse(response.data)

          // Just update loading state
          set({ loading: false })
          return validatedBusiness
        } catch (error: any) {
          console.error("Failed to fetch business:", error)
          set({
            error: error.response?.data?.message || "Failed to fetch business details",
            loading: false,
          })
          return null
        }
      },

      hasVerifiedBusiness() {
        const { businesses } = get()
        return businesses.some((business) => business.is_verified && business.status === "active")
      },

      getActiveBusiness() {
        const { businesses } = get()
        return businesses.filter((business) => business.status === "active")
      },

      getMainBusiness() {
        const { businesses } = get()
        return businesses.find((business) => business.is_main === true) || null
      },

      async fetchBusinessCategories() {
        try {
          // Set initial state
          set({ loading: true, error: null })

          // Make the API request
          console.log("Fetching business categories...")
          const response = await axiosInstance.get(`/business/categories/`)
          console.log("Categories API response received")

          // Log the raw response for debugging
          // console.log("Raw API response:", JSON.stringify(response.data).substring(0, 200) + "...")

          // Extract categories from the response with proper error handling
          let categoriesData: any[] = []

          if (!response || !response.data) {
            console.error("Invalid API response: missing data")
            set({
              categories: [],
              loading: false,
              error: "Invalid API response: missing data",
            })
            return
          }

          if (Array.isArray(response.data)) {
            categoriesData = response.data
          } else if (
            response.data &&
            response.data.results &&
            Array.isArray(response.data.results)
          ) {
            categoriesData = response.data.results
          } else {
            console.log("Response is not in expected format, trying to parse as JSON")
            try {
              // Try to parse the response if it's a string
              if (typeof response.data === "string") {
                const parsedData = JSON.parse(response.data)
                if (Array.isArray(parsedData)) {
                  categoriesData = parsedData
                } else if (parsedData && Array.isArray(parsedData.results)) {
                  categoriesData = parsedData.results
                }
              }
            } catch (parseError) {
              console.error("Failed to parse response data:", parseError)
            }
          }

          // Log the first two categories to inspect structure
          if (categoriesData.length > 0) {
            // console.log(
            //   "Category structure sample (first 2 items):",
            //   JSON.stringify(categoriesData.slice(0, 2), null, 2),
            // )
            // console.log("Total categories count:", categoriesData.length)
          } else {
            // console.log("No categories found in response")
            console.log("Response data:", response.data)
          }

          // Sort categories alphabetically by name
          categoriesData.sort((a, b) => {
            const nameA = a.name ? a.name.toLowerCase() : ""
            const nameB = b.name ? b.name.toLowerCase() : ""
            return nameA.localeCompare(nameB)
          })

          set({
            categories: categoriesData,
            loading: false,
          })
        } catch (error: any) {
          console.error("Failed to fetch business categories:", error)
          set({
            error: error.response?.data?.message || "Failed to fetch business categories",
            loading: false,
          })
        }
      },

      async fetchProducts() {
        try {
          set({ productsLoading: true, error: null })

          const mainBusiness = get().businesses.find((business) => business.is_main === true)

          if (!mainBusiness) {
            console.error("No main business available")
            set({
              error: "No main business available",
              productsLoading: false,
            })
            return
          }

          // console.log("Main business found:", mainBusiness.name)
          // console.log("Products in main business:", mainBusiness.products)

          if (mainBusiness.products && Array.isArray(mainBusiness.products)) {
            // Transform products to ensure they match our expected format
            const transformedProducts = mainBusiness.products.map((product) => {
              // Handle case where product might be an Object type
              const productObj = typeof product === "object" ? product : {}

              return {
                id: productObj.id || String(Math.random()),
                name: productObj.name || "Unnamed Product",
                price:
                  typeof productObj.price === "number"
                    ? productObj.price
                    : typeof productObj.price === "string"
                      ? parseFloat(productObj.price)
                      : 0,
                description: productObj.description || "",
                // Add other fields with sensible defaults
              }
            })

            // console.log("Transformed products:", transformedProducts)

            set({
              products: transformedProducts,
              productsLoading: false,
            })
          } else {
            console.log("No products found in main business or products is not an array")
            set({
              products: [],
              productsLoading: false,
            })
          }

          // if (currentBusiness.products) {
          //   console.log("Products property type:", typeof currentBusiness.products)
          //   console.log("Products values:", currentBusiness.products)
          // }
          // Log the entire current business object structure
          // console.log("Current business structure:", JSON.stringify(currentBusiness, null, 2))

          // Extract products from the current business
          // if (currentBusiness.products) {
          //   console.log("Products property type:", typeof currentBusiness.products)
          //   console.log("Products values:", currentBusiness.products)

          //   // Check if it's an array
          //   if (Array.isArray(currentBusiness.products)) {
          //     console.log(
          //       `Found ${currentBusiness.products.length} products in current business array`,
          //     )
          //   } else if (typeof currentBusiness.products === "object") {
          //     // If it's an object, try to convert it to an array
          //     const productsArray = Object.values(currentBusiness.products)
          //     console.log(`Converted products object to array with ${productsArray.length} items`)

          //     // Replace the products property with the array for further processing
          //     currentBusiness.products = productsArray
          //   }

          //   // Transform products to ensure they match our expected format
          //   let productsToProcess = currentBusiness.products

          //   // Special handling for products that might be nested in [Object] format
          //   if (
          //     productsToProcess.length > 0 &&
          //     productsToProcess[0] &&
          //     productsToProcess[0].toString() === "[object Object]"
          //   ) {
          //     console.log("Products appear to be nested objects, attempting to unwrap")

          //     // Try to unwrap the objects
          //     const unwrappedProducts = []
          //     for (const item of productsToProcess) {
          //       // Convert to a regular object if it's a special object
          //       const unwrapped = JSON.parse(JSON.stringify(item))
          //       console.log("Unwrapped product:", unwrapped)
          //       unwrappedProducts.push(unwrapped)
          //     }

          //     if (unwrappedProducts.length > 0) {
          //       console.log(`Successfully unwrapped ${unwrappedProducts.length} products`)
          //       productsToProcess = unwrappedProducts
          //     }
          //   }

          //   console.log("Processing products:", productsToProcess)

          //   const transformedProducts = productsToProcess.map((product) => {
          //     // Handle case where product might be an Object type
          //     const productObj = typeof product === "object" ? product : {}

          //     console.log("Processing product:", productObj)

          //     return {
          //       id: productObj.id || String(Math.random()),
          //       name: productObj.name || "Unnamed Product",
          //       price:
          //         typeof productObj.price === "number"
          //           ? productObj.price
          //           : typeof productObj.price === "string"
          //             ? parseFloat(productObj.price)
          //             : 0,
          //       description: productObj.description || "",
          //       // Add other fields with sensible defaults
          //     }
          //   })

          //   console.log("Transformed products:", transformedProducts)

          //   set({
          //     products: transformedProducts,
          //     productsLoading: false,
          //   })
          // } else {
          //   console.log("No products found in current business")
          //   set({
          //     products: [],
          //     productsLoading: false,
          //   })
          // }
        } catch (error: any) {
          console.error("Failed to process products:", error)
          set({
            error: error.message || "Failed to process products",
            productsLoading: false,
          })
        }
      },
    }),
    {
      name: "business-storage",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => storage.set(key, JSON.stringify(value)),
        removeItem: (key) => storage.delete(key),
      })),
    },
  ),
)
